<compatibility-matrix version="1.0" type="framework" level="5">
    <!-- Framework compatibility matrix -->
    
    <!-- System services that the framework expects -->
    
    <!-- Keystore service -->
    <hal format="aidl" optional="false">
        <name>android.system.keystore2</name>
        <version>1</version>
        <interface>
            <name>IKeystoreService</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Allocator service -->
    <hal format="hidl" optional="false">
        <name>android.hidl.allocator</name>
        <version>1.0</version>
        <interface>
            <name>IAllocator</name>
            <instance>ashmem</instance>
        </interface>
    </hal>

    <!-- Memory service -->
    <hal format="hidl" optional="false">
        <name>android.hidl.memory</name>
        <version>1.0</version>
        <interface>
            <name>IMapper</name>
            <instance>ashmem</instance>
        </interface>
    </hal>

    <!-- Token service -->
    <hal format="hidl" optional="false">
        <name>android.hidl.token</name>
        <version>1.0</version>
        <interface>
            <name>ITokenManager</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Manager service -->
    <hal format="hidl" optional="false">
        <name>android.hidl.manager</name>
        <version>1.2</version>
        <interface>
            <name>IServiceManager</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Base service -->
    <hal format="hidl" optional="false">
        <name>android.hidl.base</name>
        <version>1.0</version>
    </hal>

    <!-- Safe Union -->
    <hal format="hidl" optional="false">
        <name>android.hidl.safe_union</name>
        <version>1.0</version>
    </hal>

    <!-- Vendor services that framework may use -->
    
    <!-- MediaTek services -->
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.mtkpower</name>
        <version>1.0-2</version>
        <interface>
            <name>IMtkPower</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Qualcomm services -->
    <hal format="hidl" optional="true">
        <name>vendor.qti.hardware.vibrator</name>
        <version>1.0-3</version>
        <interface>
            <name>IVibrator</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- System properties -->
    <system-sdk>
        <version>30-33</version>
    </system-sdk>

    <!-- Kernel requirements -->
    <kernel version="4.14.0" level="5">
        <config>
            <key>CONFIG_ANDROID_BINDER_IPC</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_BINDERFS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_BINDER_DEVICES</key>
            <value type="string">binder,hwbinder,vndbinder</value>
        </config>
        <config>
            <key>CONFIG_ASHMEM</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_STAGING</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ION</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_ANDROID_LOW_MEMORY_KILLER</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_SELINUX</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_SELINUX_BOOTPARAM</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_SELINUX_DISABLE</key>
            <value type="tristate">n</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_SELINUX_DEVELOP</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_SELINUX_AVC_STATS</key>
            <value type="tristate">y</value>
        </config>
        <config>
            <key>CONFIG_SECURITY_SELINUX_CHECKREQPROT_VALUE</key>
            <value type="int">0</value>
        </config>
        <config>
            <key>CONFIG_AUDIT</key>
            <value type="tristate">y</value>
        </config>
    </kernel>

    <!-- Vendor interface requirements -->
    <vendor-ndk>
        <version>30</version>
    </vendor-ndk>

    <!-- VNDK requirements -->
    <vndk>
        <version>30</version>
        <library>libbinder</library>
        <library>libhidlbase</library>
        <library>libhidltransport</library>
        <library>libhwbinder</library>
        <library>libbase</library>
        <library>libutils</library>
        <library>libcutils</library>
        <library>liblog</library>
        <library>libvndksupport</library>
    </vndk>

</compatibility-matrix>
