
<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Copyright 2009, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<!-- These resources are around just to allow their values to be customized
     for different hardware and product builds. -->
<resources>
    <!-- Should we vibrate on an icon animation of the shelf. This should only be active if the
     vibrator is capable of subtle vibrations -->
    <bool name="config_vibrateOnIconAnimation">true</bool>

    <!-- SystemUI configuration to fix crashes and errors -->

    <!-- Disable display cutout protection to prevent configuration crashes -->
    <bool name="config_enableDisplayCutoutProtection">false</bool>

    <!-- Clock configuration to prevent parsing errors -->
    <bool name="config_enableClockRegistry">false</bool>

    <!-- Disable problematic SystemUI features -->
    <bool name="config_enableOverviewProxyService">false</bool>

    <!-- Disable features that require Google services -->
    <bool name="config_enableSmartspace">false</bool>

    <!-- Disable problematic transitions -->
    <bool name="config_enableTransitionChain">false</bool>

    <!-- Disable features causing tombstone errors -->
    <bool name="config_enableNativeTombstoneManager">false</bool>

    <!-- Wallpaper configuration -->
    <bool name="config_enableWallpaperCropper">false</bool>

    <!-- Disable problematic biometric features -->
    <bool name="config_enableBiometricService">false</bool>

    <!-- Disable lockout reset tracker to prevent binder death -->
    <bool name="config_enableLockoutResetTracker">false</bool>

    <!-- App widget configuration -->
    <bool name="config_enableAppWidgetManager">false</bool>

    <!-- Disable shared connectivity manager -->
    <bool name="config_enableSharedConnectivityManager">false</bool>

    <!-- Performance configuration -->
    <bool name="config_enablePerformanceHints">false</bool>

    <!-- Disable problematic window features -->
    <bool name="config_enableWindowOrganizerController">false</bool>

    <!-- Disable ADB debugging manager errors -->
    <bool name="config_enableAdbDebuggingManager">false</bool>

</resources>
