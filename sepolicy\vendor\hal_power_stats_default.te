# Device-specific extensions for Power Stats HAL

# Allow power stats HAL to access power-related files
allow hal_power_stats_default sysfs:dir r_dir_perms;
allow hal_power_stats_default sysfs:file r_file_perms;

# Allow access to power supply information
allow hal_power_stats_default sysfs_batteryinfo:dir r_dir_perms;
allow hal_power_stats_default sysfs_batteryinfo:file r_file_perms;

# Allow access to CPU power information
allow hal_power_stats_default sysfs_devices_system_cpu:dir r_dir_perms;
allow hal_power_stats_default sysfs_devices_system_cpu:file r_file_perms;

# Allow access to thermal information
allow hal_power_stats_default proc_thermal:dir r_dir_perms;
allow hal_power_stats_default proc_thermal:file r_file_perms;

# Allow binder communication
binder_use(hal_power_stats_default)

# Allow access to vendor files
allow hal_power_stats_default vendor_file:dir search;
allow hal_power_stats_default vendor_file:file r_file_perms;
