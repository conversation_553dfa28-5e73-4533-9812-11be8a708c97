# Device-specific extensions for Boot Control HAL

# Allow boot control HAL to access boot partitions
allow hal_bootctl_default block_device:dir search;
allow hal_bootctl_default boot_block_device:blk_file rw_file_perms;

# Allow access to misc partition for boot control
allow hal_bootctl_default misc_block_device:blk_file rw_file_perms;

# Allow reading boot parameters
allow hal_bootctl_default proc_cmdline:file r_file_perms;
allow hal_bootctl_default sysfs_boot_type:file r_file_perms;

# Allow capability for raw I/O operations
allow hal_bootctl_default self:capability sys_rawio;

# Allow access to vendor files
allow hal_bootctl_default vendor_file:dir search;
allow hal_bootctl_default vendor_file:file r_file_perms;

# Allow binder communication
binder_use(hal_bootctl_default)
