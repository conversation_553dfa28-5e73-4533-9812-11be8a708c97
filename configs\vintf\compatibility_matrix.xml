<compatibility-matrix version="1.0" type="device">
    <!-- Device compatibility matrix -->
    
    <!-- Boot Control HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.boot</name>
        <version>1.2</version>
        <interface>
            <name>IBootControl</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- KeyMint HAL -->
    <hal format="aidl" optional="false">
        <name>android.hardware.security.keymint</name>
        <version>3</version>
        <interface>
            <name>IKeyMintDevice</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Power Stats HAL -->
    <hal format="hidl" optional="true">
        <name>android.hardware.power.stats</name>
        <version>1.0</version>
        <interface>
            <name>IPowerStats</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Memtrack HAL -->
    <hal format="hidl" optional="true">
        <name>android.hardware.memtrack</name>
        <version>1.0</version>
        <interface>
            <name>IMemtrack</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Thermal HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.thermal</name>
        <version>2.0</version>
        <interface>
            <name>IThermal</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Health HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.health</name>
        <version>2.1</version>
        <interface>
            <name>IHealth</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Audio HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.audio</name>
        <version>7.0</version>
        <interface>
            <name>IDevicesFactory</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Camera HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.camera.provider</name>
        <version>2.6</version>
        <interface>
            <name>ICameraProvider</name>
            <instance>legacy/0</instance>
        </interface>
    </hal>

    <!-- Graphics HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.graphics.allocator</name>
        <version>4.0</version>
        <interface>
            <name>IAllocator</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Composer HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.graphics.composer</name>
        <version>2.4</version>
        <interface>
            <name>IComposer</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Sensors HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.sensors</name>
        <version>2.1</version>
        <interface>
            <name>ISensors</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Light HAL -->
    <hal format="hidl" optional="true">
        <name>android.hardware.light</name>
        <version>2.0</version>
        <interface>
            <name>ILight</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Bluetooth HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.bluetooth</name>
        <version>1.1</version>
        <interface>
            <name>IBluetoothHci</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- WiFi HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.wifi</name>
        <version>1.5</version>
        <interface>
            <name>IWifi</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- USB HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.usb</name>
        <version>1.3</version>
        <interface>
            <name>IUsb</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Vibrator HAL -->
    <hal format="hidl" optional="true">
        <name>vendor.qti.hardware.vibrator</name>
        <version>1.3</version>
        <interface>
            <name>IVibrator</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Fingerprint HAL -->
    <hal format="hidl" optional="true">
        <name>android.hardware.biometrics.fingerprint</name>
        <version>2.3</version>
        <interface>
            <name>IBiometricsFingerprint</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- DRM HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.drm</name>
        <version>1.4</version>
        <interface>
            <name>ICryptoFactory</name>
            <instance>default</instance>
        </interface>
        <interface>
            <name>IDrmFactory</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- RIL HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.radio</name>
        <version>1.6</version>
        <interface>
            <name>IRadio</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
        </interface>
    </hal>

    <!-- GNSS HAL -->
    <hal format="hidl" optional="true">
        <name>android.hardware.gnss</name>
        <version>2.1</version>
        <interface>
            <name>IGnss</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Media Codec HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.media.c2</name>
        <version>1.2</version>
        <interface>
            <name>IComponentStore</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- Gatekeeper HAL -->
    <hal format="hidl" optional="false">
        <name>android.hardware.gatekeeper</name>
        <version>1.0</version>
        <interface>
            <name>IGatekeeper</name>
            <instance>default</instance>
        </interface>
    </hal>

    <!-- MediaTek Power HAL -->
    <hal format="hidl" optional="true">
        <name>vendor.mediatek.hardware.mtkpower</name>
        <version>1.2</version>
        <interface>
            <name>IMtkPower</name>
            <instance>default</instance>
        </interface>
    </hal>

</compatibility-matrix>
