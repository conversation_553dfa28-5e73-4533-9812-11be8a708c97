--------- beginning of system
07-20 20:20:04.684   590   590 E vold    : Error getting bootctrl v1.0 module.
07-20 20:20:04.856   633   641 E keystore2: system/security/keystore2/src/error.rs:183 - system/security/keystore2/src/maintenance.rs:177: getting keymint device
07-20 20:20:04.856   633   641 E keystore2: 
07-20 20:20:04.856   633   641 E keystore2: Caused by:
07-20 20:20:04.856   633   641 E keystore2:     0: system/security/keystore2/src/globals.rs:348: Cannot connect to Keymint
07-20 20:20:04.856   633   641 E keystore2:     1: system/security/keystore2/src/globals.rs:264: Trying to get Legacy wrapper. Attempt to get keystore compat service for security level r#STRONGBOX
07-20 20:20:04.856   633   641 E keystore2:     2: Error::Km(r#HARDWARE_TYPE_UNAVAILABLE)
07-20 20:20:04.856   590   590 E vold    : keystore2 Keystore earlyBootEnded returned service specific error: -68
07-20 20:20:09.235  1298  1335 E OomConnection: failed waiting for OOM events: java.lang.RuntimeException: Failed to initialize memevents listener
07-20 20:20:09.359  1298  1324 E LocalDisplayAdapter: Can't find display mode with id -1
07-20 20:20:09.362  1298  1324 E DisplayManagerService: Default display is null for info request from uid 1000
07-20 20:20:12.731  1000  1000 E storaged: getService package_native failed
07-20 20:20:13.521  1298  1298 E PackageManager: There should probably be exactly one setup wizard; found 0: matches=[]
07-20 20:20:14.626  1298  1298 E DeviceStateManagerService: Cannot notify device state info change before the initial state has been committed.
07-20 20:20:16.521  1298  1553 E SystemServiceRegistry: Manager wrapper not available: jobscheduler
07-20 20:20:16.629  1298  1298 E WiredAccessoryManager: file /sys/devices/platform/soc/soc:qcom,msm-ext-disp/extcon/extcon3/name not found
07-20 20:20:16.629  1298  1298 E WiredAccessoryManager: file /sys/devices/platform/soc/soc:qcom,msm-ext-disp/extcon/extcon2/name not found
07-20 20:20:16.629  1298  1298 E WiredAccessoryManager: file /sys/devices/platform/soc/soc:qcom,msm-ext-disp/extcon/extcon1/name not found
07-20 20:20:16.629  1298  1298 E WiredAccessoryManager: file /sys/devices/platform/soc/soc:qcom,msm-ext-disp/extcon/extcon0/name not found
07-20 20:20:16.773  1298  1298 E AuthService: Unknown modality: 2
07-20 20:20:16.924  1298  1298 E AutofillManagerServiceImpl: Bad service name: com.google.android.gms/.autofill.service.AutofillService
07-20 20:20:16.963  1298  1298 E StatsPullAtomCallbackImpl: Failed to start PowerStatsService statsd pullers
07-20 20:20:16.967  1298  1298 E BatteryStatsService: Could not register PowerStatsInternal
07-20 20:20:16.995  1298  1298 E DisplayPowerController[0]: failed to set up display white-balance: java.lang.IllegalStateException: cannot find light sensor
07-20 20:20:17.003  1298  1298 E DeviceConfig: Parsing int failed for location_mode
07-20 20:20:17.213  1298  1298 E UserManagerService: Auto-lock preference updated but private space user not found
07-20 20:20:17.492  1298  1298 E ActivityManager: Unable to find net.slions.fulguris.full.download/u0
07-20 20:20:17.537  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.573  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.580  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.585  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.632  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.642  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.649  1298  1298 E WallpaperCropper: Unable to apply new wallpaper
07-20 20:20:17.649  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.653  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.662  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.666  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.678  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.688  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.695  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.706  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.714  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.715  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.715  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.715  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.716  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.716  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.717  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.717  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.718  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.718  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.719  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.719  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.720  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.720  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.720  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.721  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.721  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.722  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.722  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.723  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.723  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.724  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.724  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.724  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.725  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.725  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.726  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.726  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.727  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.727  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.728  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.728  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.729  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.729  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.730  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.730  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.731  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.731  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.732  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.732  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.733  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.733  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.734  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.734  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.735  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.735  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.736  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.736  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.737  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.737  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.737  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.738  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.738  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.739  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.740  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.740  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.741  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.741  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.741  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.742  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.742  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.743  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.744  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.744  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.744  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.745  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.745  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.746  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.746  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.747  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.747  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.747  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.748  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.748  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.749  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.749  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.750  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.750  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.751  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.751  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.751  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.752  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.752  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.753  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.753  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.754  1298  1549 E NativeTombstoneManager: Tombstone's UID (1000) not an app, ignoring
07-20 20:20:17.783  1298  1546 E JobScheduler.Quota: maybeScheduleStartAlarmLocked called for <0>com.android.permissioncontroller that has no jobs
07-20 20:20:17.784  1298  1546 E JobScheduler.Quota: maybeScheduleStartAlarmLocked called for <0>com.android.permissioncontroller that has no jobs
07-20 20:20:18.002  1298  1571 E AppOps  : Bad call made by uid 1000. Package "android" does not belong to uid 1037.
07-20 20:20:18.007  1298  1571 E AppOps  : Cannot checkOperation: non-application UID 1037
07-20 20:20:18.009  1298  1571 E AppOps  : Bad call made by uid 1000. Package "android" does not belong to uid 1037.
07-20 20:20:18.009  1298  1571 E AppOps  : Cannot checkOperation: non-application UID 1037
07-20 20:20:18.058  1298  1534 E TransitionChain: Can't collect into a chain with no transition
07-20 20:20:18.058  1298  1534 E TransitionChain: Can't collect into a chain with no transition
07-20 20:20:18.647  1298  1298 E AppOps  : package com.android.server.telecom.callsequencing.voip not found, can't check for attributionTag null
07-20 20:20:18.670  1298  1298 E ContentSuggestionsPerUserService: Bad service name: com.google.android.as/com.google.android.apps.miphone.aiai.app.AiAiContentSuggestionsService
07-20 20:20:18.671  1298  1298 E AutofillManagerServiceImpl: Bad service name: com.google.android.gms/.autofill.service.AutofillService
07-20 20:20:19.223  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.224  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.231  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.233  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.494  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.495  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.496  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.497  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.501  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.503  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.505  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.505  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.718  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.719  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:19.991  1298  1314 E WindowManager: Display reconfigured outside of a transition: Display{#0 state=ON size=1080x2400 ROTATION_0}
07-20 20:20:20.223  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.225  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.292  1298  1324 E PowerStatsService: Failed to start PowerStatsService loggers
07-20 20:20:20.338  1298  1673 E SystemServiceRegistry: Manager wrapper not available: uwb
07-20 20:20:20.340  1298  1673 E SystemServiceRegistry: Manager wrapper not available: thread_network
07-20 20:20:20.367  1298  1887 E AutofillManagerServiceImpl: Bad service name: com.google.android.gms/.autofill.service.AutofillService
07-20 20:20:20.445  1298  2263 E AdbDebuggingManager: Caught an exception opening the socket: java.io.IOException: Connection refused
07-20 20:20:20.564  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.571  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.667  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.668  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.692  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.694  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.796  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.800  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.807  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.809  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.844  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.846  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:20.960  1761  1761 E TransactionExecutor: Failed to execute the transaction: tId:-1651582641 ClientTransaction{
07-20 20:20:20.960  1761  1761 E TransactionExecutor: tId:-1651582641   transactionItems=[
07-20 20:20:20.960  1761  1761 E TransactionExecutor: tId:-1651582641     ConfigurationChangeItem{deviceId=0, config{1.0 ?mcc0mnc [en_IN] ldltr sw420dp w420dp h934dp 411dpi nrml long hdr port night finger -keyb/v/h -nav/h winConfig={ mBounds=Rect(0, 0 - 1080, 2400) mAppBounds=Rect(0, 0 - 1080, 2400) mMaxBounds=Rect(0, 0 - 1080, 2400) mDisplayRotation=ROTATION_0 mWindowingMode=fullscreen mActivityType=undefined mAlwaysOnTop=undefined mRotation=ROTATION_0} as.2 s.22 fontWeightAdjustment=0}}
07-20 20:20:20.960  1761  1761 E TransactionExecutor: tId:-1651582641     WindowContextInfoChangeItem{clientToken=android.window.WindowTokenClient@28cbe0a, info=WindowContextInfo{config={1.0 ?mcc0mnc [en_IN] ldltr sw420dp w420dp h934dp 411dpi nrml long hdr port night finger -keyb/v/h -nav/h winConfig={ mBounds=Rect(0, 0 - 1080, 2400) mAppBounds=Rect(0, 0 - 1080, 2400) mMaxBounds=Rect(0, 0 - 1080, 2400) mDisplayRotation=ROTATION_0 mWindowingMode=fullscreen mActivityType=undefined mAlwaysOnTop=undefined mRotation=ROTATION_0} as.2 s.12 fontWeightAdjustment=0}, displayId=0}}
07-20 20:20:20.960  1761  1761 E TransactionExecutor: tId:-1651582641     WindowContextInfoChangeItem{clientToken=android.window.WindowTokenClient@19fb621, info=WindowContextInfo{config={1.0 ?mcc0mnc [en_IN] ldltr sw420dp w420dp h934dp 411dpi nrml long hdr port night finger -keyb/v/h -nav/h winConfig={ mBounds=Rect(0, 0 - 1080, 2400) mAppBounds=Rect(0, 0 - 1080, 2400) mMaxBounds=Rect(0, 0 - 1080, 2400) mDisplayRotation=ROTATION_0 mWindowingMode=fullscreen mActivityType=undefined mAlwaysOnTop=undefined mRotation=ROTATION_0} as.2 s.12 fontWeightAdjustment=0}, displayId=0}}
07-20 20:20:20.960  1761  1761 E TransactionExecutor: tId:-1651582641     WindowContextInfoChangeItem{clientToken=android.window.WindowTokenClient@1772883, info=WindowContextInfo{config={1.0 ?mcc0mnc [en_IN] ldltr sw420dp w420dp h934dp 411dpi nrml long hdr port night finger -keyb/v/h -nav/h winConfig={ mBounds=Rect(0, 0 - 1080, 2400) mAppBounds=Rect(0, 0 - 1080, 2400) mMaxBounds=Rect(0, 0 - 1080, 2400) mDisplayRotation=ROTATION_0 mWindowingMode=fullscreen mActivityType=undefined mAlwaysOnTop=undefined mRotation=ROTATION_0} as.2 s.12 fontWeightAdjustment=0}, displayId=0}}
07-20 20:20:20.960  1761  1761 E TransactionExecutor: tId:-1651582641   ]
07-20 20:20:20.960  1761  1761 E TransactionExecutor: tId:-1651582641 }
--------- beginning of crash
07-20 20:20:20.960  1761  1761 E AndroidRuntime: FATAL EXCEPTION: main
07-20 20:20:20.960  1761  1761 E AndroidRuntime: Process: com.android.systemui, PID: 1761
07-20 20:20:20.960  1761  1761 E AndroidRuntime: java.util.ConcurrentModificationException
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at java.util.ArrayList$Itr.checkForComodification(ArrayList.java:1111)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at java.util.ArrayList$Itr.next(ArrayList.java:1064)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at kotlin.collections.CollectionsKt.filterNotNull(go/retraceme d1b7bf4c15c8ed537fdb83e1110da3faf7eaa3d29b192b280b756d43ed4d2009:16)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at com.android.systemui.statusbar.phone.ConfigurationControllerImpl.onConfigurationChanged(go/retraceme d1b7bf4c15c8ed537fdb83e1110da3faf7eaa3d29b192b280b756d43ed4d2009:30)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at com.android.systemui.SystemUIApplication.onConfigurationChanged(go/retraceme d1b7bf4c15c8ed537fdb83e1110da3faf7eaa3d29b192b280b756d43ed4d2009:38)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.ConfigurationController.performConfigurationChanged(ConfigurationController.java:265)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.ConfigurationController.handleConfigurationChangedInner(ConfigurationController.java:239)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.ConfigurationController.handleConfigurationChanged(ConfigurationController.java:157)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.ConfigurationController.handleConfigurationChanged(ConfigurationController.java:132)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.ActivityThread.handleConfigurationChanged(ActivityThread.java:6782)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.servertransaction.ConfigurationChangeItem.execute(ConfigurationChangeItem.java:56)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:133)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:103)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:80)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2782)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:109)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:232)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:317)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:8969)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
07-20 20:20:20.960  1761  1761 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:911)
--------- beginning of main
07-20 20:20:21.038  1298  1573 E InputDispatcher: channel '6862d83 NotificationShade' ~ Channel is unrecoverably broken and will be disposed!
07-20 20:20:21.040  1298  1573 E InputDispatcher: channel 'ab2ec71 StatusBar' ~ Channel is unrecoverably broken and will be disposed!
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: Failed to deliver pending transaction
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: android.os.DeadObjectException
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at android.os.BinderProxy.transactNative(Native Method)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at android.os.BinderProxy.transact(BinderProxy.java:592)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at android.app.IApplicationThread$Stub$Proxy.scheduleTransaction(IApplicationThread.java:2327)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at android.app.IApplicationThread$Delegator.scheduleTransaction(IApplicationThread.java:442)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at android.app.servertransaction.ClientTransaction.schedule(ClientTransaction.java:241)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at com.android.server.wm.ClientLifecycleManager.scheduleTransaction(go/retraceme b1d093d1655900eef0c7d61aac3b993c7754cf145d84d42fe519b758a20a8e8f:5)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at com.android.server.wm.ClientLifecycleManager.dispatchPendingTransactions(go/retraceme b1d093d1655900eef0c7d61aac3b993c7754cf145d84d42fe519b758a20a8e8f:34)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at com.android.server.wm.RootWindowContainer.performSurfacePlacementNoTrace(go/retraceme b1d093d1655900eef0c7d61aac3b993c7754cf145d84d42fe519b758a20a8e8f:599)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at com.android.server.wm.WindowSurfacePlacer.performSurfacePlacement(go/retraceme b1d093d1655900eef0c7d61aac3b993c7754cf145d84d42fe519b758a20a8e8f:161)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at com.android.server.wm.WindowState.removeImmediately(go/retraceme b1d093d1655900eef0c7d61aac3b993c7754cf145d84d42fe519b758a20a8e8f:516)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at com.android.server.wm.WindowState.removeIfPossible(go/retraceme b1d093d1655900eef0c7d61aac3b993c7754cf145d84d42fe519b758a20a8e8f:616)
07-20 20:20:21.043  1298  1573 E ClientLifecycleManager: 	at com.android.server.input.InputManagerService.notifyInputChannelBroken(go/retraceme b1d093d1655900eef0c7d61aac3b993c7754cf145d84d42fe519b758a20a8e8f:68)
07-20 20:20:21.046  1298  1573 E InputDispatcher: channel 'cbfed3b ScreenDecorOverlayBottom' ~ Channel is unrecoverably broken and will be disposed!
07-20 20:20:21.046  1298  1573 E InputDispatcher: channel 'ea0afd4 ScreenDecorOverlay' ~ Channel is unrecoverably broken and will be disposed!
07-20 20:20:21.047  1298  1314 E LockoutResetTracker: Callback binder died: android.os.BinderProxy@f515972
07-20 20:20:21.047  1298  1886 E BiometricService: Enabled callback binder died
07-20 20:20:21.047  1298  1314 E LockoutResetTracker: Removing dead callback for: com.android.systemui
07-20 20:20:21.047  1298  1573 E InputDispatcher: channel 'd2060ba com.android.systemui.wallpapers.ImageWallpaper' ~ Channel is unrecoverably broken and will be disposed!
07-20 20:20:21.051  1298  1532 E LockoutResetTracker: Callback binder died: android.os.BinderProxy@8b33158
07-20 20:20:21.052  1298  1532 E LockoutResetTracker: Removing dead callback for: com.android.systemui
07-20 20:20:21.052  1298  2286 E BiometricService: Enabled callback binder died
07-20 20:20:21.060  1298  1298 E AutofillManagerServiceImpl: Bad service name: com.google.android.gms/.autofill.service.AutofillService
07-20 20:20:21.068   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.081  1298  1298 E AppOps  : attributionTag twilight not declared in manifest of android
07-20 20:20:21.082   886   886 E BpTransactionCompletedListener: Failed to transact (-32)
07-20 20:20:21.082   886   886 E BpTransactionCompletedListener: Failed to transact (-32)
07-20 20:20:21.082   886   886 E BpTransactionCompletedListener: Failed to transact (-32)
07-20 20:20:21.083   886   886 E BpTransactionCompletedListener: Failed to transact (-32)
07-20 20:20:21.083   886   886 E BpTransactionCompletedListener: Failed to transact (-32)
07-20 20:20:21.085   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.099   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.115   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.119  1298  1324 E WindowOrganizerController: Trying to start a transition that isn't collecting. This probably means Shell took too long to respond to a request. WM State may be incorrect now, please file a bug
07-20 20:20:21.134  2275  2281 E adbd    : failed to send interruption signal to worker: No such process
07-20 20:20:21.148   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.176  2348  2348 E HidlServiceManagement: Service vendor.nxp.nxpnfc@1.0::INxpNfc/default must be in VINTF manifest in order to register/get.
07-20 20:20:21.177  2350  2364 E MtkUiccSEHal: [se1]internalReset:1170: RESET reason: INIT (0, 1)
07-20 20:20:21.182  2350  2364 E MtkUiccSEHal: [se2]internalReset:1170: RESET reason: INIT (0, 1)
07-20 20:20:21.186  2350  2377 E MtkUiccSEHal: [se2]initRadioService:873: tryGetService se2
07-20 20:20:21.188  2350  2371 E MtkUiccSEHal: [se1]initRadioService:873: tryGetService se1
07-20 20:20:21.193   577   577 E lmkd    : memevent listener failed to initialize, not supported kernel
07-20 20:20:21.193   577   577 E lowmemorykiller: Failed to initialize memevents listener
07-20 20:20:21.193   577   577 E lmkd    : memevent deregister all events failed, failure to initialize
07-20 20:20:21.198   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.298   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.305  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:21.309  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:21.325  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:21.328  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:21.331   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.335  2334  2361 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:20:21.347   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.379  2334  2412 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:20:21.397   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.401  1298  1887 E TransitionChain: Can't collect into a chain with no transition
07-20 20:20:21.402  1298  1887 E TransitionChain: Can't collect into a chain with no transition
07-20 20:20:21.414   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): Failed to parse clock settings
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): java.lang.NullPointerException: Attempt to invoke virtual method 'int java.lang.String.length()' on a null object reference
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at org.json.JSONTokener.nextCleanInternal(JSONTokener.java:121)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at org.json.JSONTokener.nextValue(JSONTokener.java:98)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at org.json.JSONObject.<init>(JSONObject.java:168)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at org.json.JSONObject.<init>(JSONObject.java:185)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at com.android.systemui.shared.clocks.ClockRegistry.querySettings(go/retraceme d1b7bf4c15c8ed537fdb83e1110da3faf7eaa3d29b192b280b756d43ed4d2009:24)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at com.android.systemui.shared.clocks.ClockRegistry$registerListeners$1.invokeSuspend(go/retraceme d1b7bf4c15c8ed537fdb83e1110da3faf7eaa3d29b192b280b756d43ed4d2009:12)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(go/retraceme d1b7bf4c15c8ed537fdb83e1110da3faf7eaa3d29b192b280b756d43ed4d2009:8)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at kotlinx.coroutines.DispatchedTask.run(go/retraceme d1b7bf4c15c8ed537fdb83e1110da3faf7eaa3d29b192b280b756d43ed4d2009:110)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:524)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at java.util.concurrent.FutureTask.run(FutureTask.java:317)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:348)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
07-20 20:20:21.426  2334  2407 E ClockRegistry (System): 	at java.lang.Thread.run(Thread.java:1119)
07-20 20:20:21.442  1950  2027 E SecureElement-AccessControlEnforcer: No ARF exists
07-20 20:20:21.451  2334  2334 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
07-20 20:20:21.520  2334  2394 E AppWidgetManager: Notify service of inheritance info
07-20 20:20:21.520  2334  2394 E AppWidgetManager: java.lang.IllegalStateException: User 0 must be unlocked for widgets to be available
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.os.Parcel.createExceptionOrNull(Parcel.java:3282)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.os.Parcel.createException(Parcel.java:3258)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.os.Parcel.readException(Parcel.java:3234)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.os.Parcel.readException(Parcel.java:3176)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at com.android.internal.appwidget.IAppWidgetService$Stub$Proxy.getInstalledProvidersForProfile(IAppWidgetService.java:1071)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.appwidget.AppWidgetManager.getInstalledProvidersForProfile(AppWidgetManager.java:1105)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.appwidget.AppWidgetManager.getInstalledProvidersForPackage(AppWidgetManager.java:1039)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.appwidget.AppWidgetManager.lambda$new$3(AppWidgetManager.java:637)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.appwidget.AppWidgetManager.$r8$lambda$WKLPyaeqLNLNb3560d7QJuq2DwQ(Unknown Source:0)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.appwidget.AppWidgetManager$$ExternalSyntheticLambda7.run(D8$$SyntheticClass:0)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.os.Handler.handleCallback(Handler.java:991)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.os.Handler.dispatchMessage(Handler.java:102)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.os.Looper.loopOnce(Looper.java:232)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.os.Looper.loop(Looper.java:317)
07-20 20:20:21.520  2334  2394 E AppWidgetManager: 	at android.os.HandlerThread.run(HandlerThread.java:85)
07-20 20:20:21.621  1298  2331 E WifiService: Attempt to retrieve passpoint with invalid scanResult List
07-20 20:20:21.627   886  2253 E HWComposer: getDisplayDecorationSupport: getDisplayDecorationSupport failed for display 0: UNSUPPORTED (8)
07-20 20:20:21.636  2334  2413 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:20:21.644  2334  2412 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:20:21.647   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.664   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.680   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.714   837  1139 E hwcomposer: [JOB] (0) Handle a job with no visible layer  
07-20 20:20:21.785  2334  2334 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
07-20 20:20:22.078  1037  1996 E Codec2-ComponentInterface: We have a failed config
07-20 20:20:22.079  2334  2466 E ion     : ioctl c0044901 failed with code -1: Invalid argument
07-20 20:20:22.091  1037  1988 E Codec2-ComponentInterface: We have a failed config
07-20 20:20:22.120  1037  1890 E Codec2-ComponentInterface: We have a failed config
07-20 20:20:22.138  2334  2334 E OverviewProxyService: Failed to get overview proxy for disable flags.
07-20 20:20:22.150  2334  2412 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:20:22.158  2334  2334 E OverviewProxyService: Failed to get overview proxy for disable flags.
07-20 20:20:22.172  2334  2412 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:20:22.183  2334  2412 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:20:22.229  2334  2334 E OverviewProxyService: Failed to get overview proxy for disable flags.
07-20 20:20:22.266  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:22.267  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:22.313  2334  2413 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:20:22.416  2334  2334 E PhoneStatusBarView: mHasCornerCutoutFetcher unexpectedly null
07-20 20:20:22.416  2334  2334 E PhoneStatusBarView: mInsetsFetcher unexpectedly null
07-20 20:20:22.499  1298  2331 E AutofillManagerServiceImpl: Bad service name: com.google.android.gms/.autofill.service.AutofillService
07-20 20:20:22.546  2334  2334 E StackScroller: Attempting to re-position  view {null}
07-20 20:20:22.546  2334  2334 E StackScroller: Attempting to re-position  view {null}
07-20 20:20:22.577  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:22.577  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:22.598  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:22.599  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:22.620  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:22.621  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:22.643  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:22.644  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:22.919  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:22.920  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:23.110  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:23.111  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:23.135  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:23.136  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:23.211  2334  2334 E PhoneStatusBarView: mHasCornerCutoutFetcher unexpectedly null
07-20 20:20:23.211  2334  2334 E PhoneStatusBarView: mInsetsFetcher unexpectedly null
07-20 20:20:23.475  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:23.476  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:23.488  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:23.488  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:23.498  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:23.498  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:23.512  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:23.513  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:23.520  2290  2316 E UsageReportingOptionsSt: INTERNAL_ERROR: can't query optInOptions while user is locked. [CONTEXT service_id=41 ]
07-20 20:20:23.862  2506  2548 E GservicesValue: Gservices key not allowlisted for directboot access: gms:chimera:auto_stage_all_test_apks
07-20 20:20:24.055   991  1020 E flp     : mnld_screen_monitor_thread: Screen off
07-20 20:20:24.062  1046  1233 E [GF_HAL][Device]: module:3, event:2
07-20 20:20:24.062  1046  1233 E [GF_HAL][Device]: start wait event
07-20 20:20:24.085  1986  1986 E TelephonyProvider: internal sim-specific settings backup data file does not exist. Aborting restore
07-20 20:20:24.126  1986  1986 E TelephonyProvider: internal sim-specific settings backup data file does not exist. Aborting restore
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute oplus5gSupportRegion=TRUE
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=CMCC
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=CT
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=CU
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=3HK
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=CMHK
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=HKT
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SMARTONE
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=CHT
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=FET
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TWM
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=APTG
07-20 20:20:24.226  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TST
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SINGTEL
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=STARHUB
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=M1
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TPG
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DIGI
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=YES
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=WEBE
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Celcom
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Maxis
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=UMobile
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TELSTRA
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=OPTUS
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VDF
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=2DEGREE
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SPARK
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VDF-NZ
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=AIS
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DTAC
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TRUE
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=GLOBE
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SMART
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DITO
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Vinaphone
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VIETTEL
07-20 20:20:24.227  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VIETNAMOBILE
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TELENOR
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=MYTEL
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ZONG
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TELENOR
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Robi
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Grameenphone
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mobitel
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DIALOG
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=STC
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ZAIN
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SWISSCOM
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SUNRISE
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SALT
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Yoigo
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-Vodafone
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-Movistar
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-Telecable
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-Tuenti
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Proximus
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Telenet
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Belgium-Orange
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=France-Bouygues
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=France-SFR
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=France-ORANGE
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Italy-TIM
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Italy-Windtre
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Italy-Vodafone
07-20 20:20:24.228  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Fastweb
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=UK-EE
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=UK-Vodafone
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=3 UK
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=UK-Virgin
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=O2 UK
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SKY
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=EIR
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Ireland-Vodafone
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Netherland-TELE2
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Netherland-KPN
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Netherland-Tmobile
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Netherland-Vodafone
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Poland-Play
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Poland-Plus
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Poland-Orange
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Romania-Digi
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Romania-Orange
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Romania-Vodafone
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=MEO
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Portugal NOS
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Portugal-Vodafone
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=BASE
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Telenet
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Belgium-Orange
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Germany-Vodafone
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Germany-Tmobile
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Germany-Telefonica
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Lux-Orange
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Tango
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=POST
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mol-Orange
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Croatia-DT
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Czech-O2
07-20 20:20:24.229  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Czech-VDF
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Czech-DT
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Greece-VDF
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Greece-TMobile
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Hungary-VDF
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Hungary-DT
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Slovakia-O2
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Slovakia-Orange
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Slovakia-DT
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Vivacom
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Russia-Megafon
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Russia-MTS
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Beeline
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Tele2
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=JIO
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VDF
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=IDEA
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=AIRTEL
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=BSNL
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ETISALAT
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DU
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=STC
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ZAIN KSA
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=MOBILY
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Qatar-VDF
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Qatar-Ooredoo
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=OOREDOO
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ZAIN
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VIVA
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=BATELCO
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VIVA
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ZAIN
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=OMANTEL
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Pelephone
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mexico-Telcel
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mexico-AT-T
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mexico-Movistar
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mexico-ALTAN
07-20 20:20:24.230  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TPG
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Lanka Bell
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Jazztel
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Salt
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=France-ORANGE
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=3 UK
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=2DEGREE
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SPARK
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=3 UK
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=France-Bouygues
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute oplus4gPrefDefault=TRUE
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SOFTBANK
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DOCOMO
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=KDDI
07-20 20:20:24.231  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Rakuten Mobile(MNO)
07-20 20:20:24.237  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:24.239  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:24.244  1986  2149 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:24.245  1986  2149 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:24.247  2555  2566 E DefaultCarrierConfigService: Unknown attribute oplus5gSupportRegion=TRUE
07-20 20:20:24.247  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=CMCC
07-20 20:20:24.247  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=CT
07-20 20:20:24.247  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=CU
07-20 20:20:24.247  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=3HK
07-20 20:20:24.247  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=CMHK
07-20 20:20:24.247  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=HKT
07-20 20:20:24.247  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SMARTONE
07-20 20:20:24.247  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=CHT
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=FET
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TWM
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=APTG
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TST
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SINGTEL
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=STARHUB
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=M1
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TPG
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DIGI
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=YES
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=WEBE
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Celcom
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Maxis
07-20 20:20:24.248  1298  1673 E WifiCarrierInfoManager: Carrier config is missing for: 2
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=UMobile
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TELSTRA
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=OPTUS
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VDF
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=2DEGREE
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SPARK
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VDF-NZ
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=AIS
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DTAC
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TRUE
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=GLOBE
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SMART
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DITO
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Vinaphone
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VIETTEL
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VIETNAMOBILE
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TELENOR
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=MYTEL
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ZONG
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TELENOR
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Robi
07-20 20:20:24.248  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Grameenphone
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mobitel
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DIALOG
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=STC
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ZAIN
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SWISSCOM
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SUNRISE
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SALT
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Yoigo
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-Vodafone
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-Movistar
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-Telecable
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-Tuenti
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Proximus
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Telenet
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Belgium-Orange
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=France-Bouygues
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=France-SFR
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=France-ORANGE
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Italy-TIM
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Italy-Windtre
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Italy-Vodafone
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Fastweb
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=UK-EE
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=UK-Vodafone
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=3 UK
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=UK-Virgin
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=O2 UK
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SKY
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=EIR
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Ireland-Vodafone
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Netherland-TELE2
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Netherland-KPN
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Netherland-Tmobile
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Netherland-Vodafone
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Poland-Play
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Poland-Plus
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Poland-Orange
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Romania-Digi
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Romania-Orange
07-20 20:20:24.249  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Romania-Vodafone
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=MEO
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Portugal NOS
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Portugal-Vodafone
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=BASE
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Telenet
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Belgium-Orange
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Germany-Vodafone
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Germany-Tmobile
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Germany-Telefonica
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Lux-Orange
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Tango
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=POST
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mol-Orange
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Croatia-DT
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Czech-O2
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Czech-VDF
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Czech-DT
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Greece-VDF
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Greece-TMobile
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Hungary-VDF
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Hungary-DT
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Slovakia-O2
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Slovakia-Orange
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Slovakia-DT
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Vivacom
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Russia-Megafon
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Russia-MTS
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Beeline
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Tele2
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=JIO
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VDF
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=IDEA
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=AIRTEL
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=BSNL
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ETISALAT
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DU
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=STC
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ZAIN KSA
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=MOBILY
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Qatar-VDF
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Qatar-Ooredoo
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=OOREDOO
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ZAIN
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VIVA
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=BATELCO
07-20 20:20:24.250  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=VIVA
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=ZAIN
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=OMANTEL
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Pelephone
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mexico-Telcel
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mexico-AT-T
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mexico-Movistar
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Mexico-ALTAN
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=TPG
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Lanka Bell
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Spain-ORANGE
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Jazztel
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Salt
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=France-ORANGE
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=3 UK
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=2DEGREE
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SPARK
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=3 UK
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=France-Bouygues
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute oplus4gPrefDefault=TRUE
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=SOFTBANK
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=DOCOMO
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=KDDI
07-20 20:20:24.251  2555  2566 E DefaultCarrierConfigService: Unknown attribute operator=Rakuten Mobile(MNO)
07-20 20:20:24.251  1986  2147 E TelephonyConfigUpdateInstallReceiver: Failed to read current content : /data/misc/telephonyconfig/valid_telephony_config.pb
07-20 20:20:24.257  1986  2147 E TelephonyConfigUpdateInstallReceiver: Failed to read current content : /data/misc/telephonyconfig/valid_telephony_config.pb
07-20 20:20:24.262  1986  2147 E TelephonyConfigUpdateInstallReceiver: Failed to read current content : /data/misc/telephonyconfig/valid_telephony_config.pb
07-20 20:20:24.265  1986  2147 E TelephonyConfigUpdateInstallReceiver: Failed to read current content : /data/misc/telephonyconfig/valid_telephony_config.pb
07-20 20:20:24.400  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:24.408  1986  2149 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:20:24.409  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:24.410  1986  2149 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:20:24.416  1986  2147 E TelephonyConfigUpdateInstallReceiver: Failed to read current content : /data/misc/telephonyconfig/valid_telephony_config.pb
07-20 20:20:24.418  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:24.418  1986  2147 E TelephonyConfigUpdateInstallReceiver: Failed to read current content : /data/misc/telephonyconfig/valid_telephony_config.pb
07-20 20:20:24.420  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:24.423  1986  2147 E TelephonyConfigUpdateInstallReceiver: Failed to read current content : /data/misc/telephonyconfig/valid_telephony_config.pb
07-20 20:20:24.425  1986  2147 E TelephonyConfigUpdateInstallReceiver: Failed to read current content : /data/misc/telephonyconfig/valid_telephony_config.pb
07-20 20:20:24.447   632  1272 E android.system.suspend-service: error writing zygote_kwl to /sys/power/wake_unlock: Invalid argument
07-20 20:20:24.628  2587  2612 E ConnectivityManager: NetworkCallback was already registered
07-20 20:20:24.871  1298  1673 E WifiDataStall: onDataConnectionStateChanged unexpected State: 1
07-20 20:20:24.914  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:24.916  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:25.101  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:25.102  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:25.658   818   818 E Netd    : no such netId 101
07-20 20:20:25.661   818   818 E Netd    : no such netId 101
07-20 20:20:25.661   818   818 E Netd    : no such netId 101
07-20 20:20:25.671  1298  1681 E ConnectivityService: Unexpected ServiceSpecificException
07-20 20:20:25.671  1298  1681 E ConnectivityService: android.os.ServiceSpecificException: Machine is not on the network (code 64)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.os.Parcel.createExceptionOrNull(Parcel.java:3288)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.os.Parcel.createException(Parcel.java:3258)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.os.Parcel.readException(Parcel.java:3234)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.os.Parcel.readException(Parcel.java:3176)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.net.connectivity.android.net.INetd$Stub$Proxy.setNetworkAllowlist(INetd.java:3791)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.net.connectivity.com.android.server.ConnectivityService.updateProfileAllowedNetworks(ConnectivityService.java:11305)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.net.connectivity.com.android.server.ConnectivityService.updateNetworkInfo(ConnectivityService.java:12021)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.net.connectivity.com.android.server.ConnectivityService.-$$Nest$mupdateNetworkInfo(ConnectivityService.java:0)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.net.connectivity.com.android.server.ConnectivityService$NetworkStateTrackerHandler.maybeHandleNetworkAgentMessage(ConnectivityService.java:5059)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.net.connectivity.com.android.server.ConnectivityService$NetworkStateTrackerHandler.handleMessage(ConnectivityService.java:5443)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.os.Handler.dispatchMessage(Handler.java:109)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.os.Looper.loopOnce(Looper.java:232)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.os.Looper.loop(Looper.java:317)
07-20 20:20:25.671  1298  1681 E ConnectivityService: 	at android.os.HandlerThread.run(HandlerThread.java:85)
07-20 20:20:25.677   818   818 E netd    : Error adding route ::/0 -> (null) ccmni2 to table 1004: File exists
07-20 20:20:26.308  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:26.309  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:30.967   818   818 E Netd    : no such netId 100
07-20 20:20:30.970   818   818 E Netd    : no such netId 100
07-20 20:20:30.971   818   818 E Netd    : no such netId 100
07-20 20:20:31.023   818   818 E netd    : Error adding route ::/0 -> (null) ccmni1 to table 1003: File exists
07-20 20:20:31.083  2653  1681 E libsigchain: Setting SIGSEGV to SIG_DFL
07-20 20:20:31.133  2653  1681 E libsigchain:   #00 pc 000044d8  /apex/com.android.art/lib64/libsigchain.so (LogStack()+200) (BuildId: f070081ce526cfcb0be54dafd4aab454)
07-20 20:20:31.133  2653  1681 E libsigchain:   #01 pc 00005194  /apex/com.android.art/lib64/libsigchain.so (sigaction64+212) (BuildId: f070081ce526cfcb0be54dafd4aab454)
07-20 20:20:31.133  2653  1681 E libsigchain:   #02 pc 0007acd4  /apex/com.android.runtime/lib64/bionic/libc.so (ApplyAttrs(short, __posix_spawnattr* const*)+132) (BuildId: 17a660cdc9cfed657d332dec76451e26)
07-20 20:20:31.133  2653  1681 E libsigchain:   #03 pc 0007a4d0  /apex/com.android.runtime/lib64/bionic/libc.so (posix_spawn(int*, char const*, __posix_spawn_file_actions* const*, __posix_spawnattr* const*, char* const*, char* const*, int (*)(char const*, char* const*, char* const*))+336) (BuildId: 17a660cdc9cfed657d332dec76451e26)
07-20 20:20:31.133  2653  1681 E libsigchain:   #04 pc 00009640  /apex/com.android.tethering/lib64/libservice-connectivity.so (android::com_android_server_connectivity_ClatCoordinator_startClatd(_JNIEnv*, _jclass*, _jobject*, _jobject*, _jobject*, _jstring*, _jstring*, _jstring*, _jstring*)+1728) (BuildId: 6cc728eda6adff8fe815fde7a81df41d)
07-20 20:20:31.133  2653  1681 E libsigchain:   #05 pc 00384700  /apex/com.android.art/lib64/libart.so (art_quick_generic_jni_trampoline+144) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.133  2653  1681 E libsigchain:   #06 pc 007b29d8  /apex/com.android.art/lib64/libart.so (nterp_helper+2152) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.133  2653  1681 E libsigchain:   #07 pc 0009e060  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.connectivity.ClatCoordinator.-$$Nest$smnative_startClatd+0) (BuildId: )
07-20 20:20:31.133  2653  1681 E libsigchain:   #08 pc 007b2954  /apex/com.android.art/lib64/libart.so (nterp_helper+2020) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.133  2653  1681 E libsigchain:   #09 pc 0009dea0  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.connectivity.ClatCoordinator$Dependencies.startClatd+0) (BuildId: )
07-20 20:20:31.133  2653  1681 E libsigchain:   #10 pc 007b37c8  /apex/com.android.art/lib64/libart.so (nterp_helper+5720) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.133  2653  1681 E libsigchain:   #11 pc 0009e390  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.connectivity.ClatCoordinator.clatStart+584) (BuildId: )
07-20 20:20:31.133  2653  1681 E libsigchain:   #12 pc 007b30c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.133  2653  1681 E libsigchain:   #13 pc 000a778c  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.connectivity.Nat464Xlat.enterStartingState+44) (BuildId: )
07-20 20:20:31.133  2653  1681 E libsigchain:   #14 pc 007b30c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #15 pc 000a7d24  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.connectivity.Nat464Xlat.start+108) (BuildId: )
07-20 20:20:31.134  2653  1681 E libsigchain:   #16 pc 007b30c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #17 pc 000a7ffc  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.connectivity.Nat464Xlat.update+84) (BuildId: )
07-20 20:20:31.134  2653  1681 E libsigchain:   #18 pc 007b30c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #19 pc 0008f8a6  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.ConnectivityService.updateLinkProperties+234) (BuildId: )
07-20 20:20:31.134  2653  1681 E libsigchain:   #20 pc 007b30c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #21 pc 0008ae6e  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.ConnectivityService.handleUpdateLinkProperties+150) (BuildId: )
07-20 20:20:31.134  2653  1681 E libsigchain:   #22 pc 007b30c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #23 pc 0008918e  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.ConnectivityService.handleNat64PrefixEvent+182) (BuildId: )
07-20 20:20:31.134  2653  1681 E libsigchain:   #24 pc 007b30c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #25 pc 00084ae0  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.ConnectivityService.-$$Nest$mhandleNat64PrefixEvent+0) (BuildId: )
07-20 20:20:31.134  2653  1681 E libsigchain:   #26 pc 007b21a4  /apex/com.android.art/lib64/libart.so (nterp_helper+52) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #27 pc 000789e4  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.ConnectivityService$DnsResolverUnsolicitedEventCallback.lambda$onNat64PrefixEvent$0+20) (BuildId: )
07-20 20:20:31.134  2653  1681 E libsigchain:   #28 pc 007b30c4  /apex/com.android.art/lib64/libart.so (nterp_helper+3924) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #29 pc 0007899c  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.ConnectivityService$DnsResolverUnsolicitedEventCallback.$r8$lambda$pdhrbFEPKYqVFL-etc9X6Cjhe6g+0) (BuildId: )
07-20 20:20:31.134  2653  1681 E libsigchain:   #30 pc 007b21a4  /apex/com.android.art/lib64/libart.so (nterp_helper+52) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #31 pc 00078954  /apex/com.android.tethering/javalib/service-connectivity.jar (android.net.connectivity.com.android.server.ConnectivityService$DnsResolverUnsolicitedEventCallback$$ExternalSyntheticLambda0.run+8) (BuildId: )
07-20 20:20:31.134  2653  1681 E libsigchain:   #32 pc 004da7ac  /system/framework/arm64/boot-framework.oat (android.os.Handler.dispatchMessage+76) (BuildId: acda2f42dd95f3aab77a860f0462f024f57f9ced)
07-20 20:20:31.134  2653  1681 E libsigchain:   #33 pc 00512194  /system/framework/arm64/boot-framework.oat (android.os.Looper.loopOnce+932) (BuildId: acda2f42dd95f3aab77a860f0462f024f57f9ced)
07-20 20:20:31.134  2653  1681 E libsigchain:   #34 pc 00511d6c  /system/framework/arm64/boot-framework.oat (android.os.Looper.loop+252) (BuildId: acda2f42dd95f3aab77a860f0462f024f57f9ced)
07-20 20:20:31.134  2653  1681 E libsigchain:   #35 pc 00504e10  /system/framework/arm64/boot-framework.oat (android.os.HandlerThread.run+576) (BuildId: acda2f42dd95f3aab77a860f0462f024f57f9ced)
07-20 20:20:31.134  2653  1681 E libsigchain:   #36 pc 0036d394  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #37 pc 00392818  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke(art::Thread*, unsigned int*, unsigned int, art::JValue*, char const*)+136) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #38 pc 0047a32c  /apex/com.android.art/lib64/libart.so (art::Thread::CreateCallback(void*)+972) (BuildId: 3bf75354b865c3e3dbdc174b0de8b113)
07-20 20:20:31.134  2653  1681 E libsigchain:   #39 pc 00073b74  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start(void*)+180) (BuildId: 17a660cdc9cfed657d332dec76451e26)
07-20 20:20:31.134  2653  1681 E libsigchain:   #40 pc 00064060  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 17a660cdc9cfed657d332dec76451e26)
07-20 20:20:31.228   818   897 E Netd    : no such netId 102
07-20 20:20:31.229   818   897 E Netd    : no such netId 102
07-20 20:20:31.230   818   897 E Netd    : no such netId 102
07-20 20:20:31.243   818   897 E netd    : Error adding route ::/0 -> (null) ccmni0 to table 1002: File exists
07-20 20:20:31.337  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:31.341  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:20:33.782  1298  2676 E GnssPsdsDownloader: No Long-Term PSDS servers were specified in the GnssConfiguration
07-20 20:20:37.244   882   949 E GpuMem  : Failed to attach bpf program to gpu_mem/gpu_mem_total tracepoint [2(No such file or directory)]
07-20 20:20:37.244   882   949 E GpuMemTracer: Cannot initialize GpuMemTracer before GpuMem
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: Exception when checking for satellite support. Assuming it is not supported for this device.
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: android.telephony.satellite.SatelliteManager$SatelliteException
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at android.telephony.satellite.SatelliteManager$5.lambda$onReceiveResult$4(SatelliteManager.java:1138)
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at android.telephony.satellite.SatelliteManager$5$$ExternalSyntheticLambda0.runOrThrow(D8$$SyntheticClass:0)
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at android.os.Binder.withCleanCallingIdentity(Binder.java:474)
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at android.telephony.satellite.SatelliteManager$5.lambda$onReceiveResult$5(SatelliteManager.java:1137)
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at android.telephony.satellite.SatelliteManager$5$$ExternalSyntheticLambda5.run(D8$$SyntheticClass:0)
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:524)
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at java.util.concurrent.FutureTask.run(FutureTask.java:317)
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:348)
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
07-20 20:21:21.132  2334  2404 E DeviceBasedSatelliteRepo: 	at java.lang.Thread.run(Thread.java:1119)
07-20 20:21:23.600  2290  2316 E CCTFunnel: Failed find or create clearcut directory.
07-20 20:22:55.773  1298  1348 E SensorManager: sensor or listener is null
07-20 20:22:55.787  1298  1298 E SensorManager: sensor or listener is null
07-20 20:22:55.850   843   843 E sensors-hal-SensorManager: batch failed 27 0 66667000 0 err = -22
07-20 20:22:55.850  1298  1322 E SensorService: sensor batch failed 0x0000001b 66667000 0 err=Invalid argument
07-20 20:22:56.022  1046  1233 E [GF_HAL][Device]: module:3, event:3
07-20 20:22:56.022  1046  1233 E [GF_HAL][Device]: start wait event
07-20 20:22:56.033   991  1020 E flp     : mnld_screen_monitor_thread: Screen on
07-20 20:22:56.033   991  1020 E flp     : mnld_screen_monitor_thread: Wake Monitor Restart
07-20 20:22:58.729  2334  2334 E KeyguardViewMediator: mHideAnimationFinishedRunnable#run
07-20 20:22:58.767  1993  2075 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:22:58.815  1298  1332 E SystemCaptionsManagerPerUserService: Bad service name: com.google.android.as/com.google.android.apps.miphone.aiai.captions.SystemCaptionsManagerService
07-20 20:22:58.817  1298  1332 E AppPredictionPerUserService: Bad service name: com.google.android.as/com.google.android.apps.miphone.aiai.app.AiAiPredictionService
07-20 20:22:58.817  1298  1332 E ContentSuggestionsPerUserService: Bad service name: com.google.android.as/com.google.android.apps.miphone.aiai.app.AiAiContentSuggestionsService
07-20 20:22:58.830  1298  1710 E BackupPasswordManager: Unable to read backup pw version
07-20 20:22:58.830  1298  1710 E BackupPasswordManager: Unable to read saved backup pw hash
07-20 20:22:58.849  2334  2361 E SingleInstanceRemoteListener: Failed remote call on null listener
07-20 20:22:58.870  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f08038e.
07-20 20:22:58.870  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f141cfd.
07-20 20:22:58.870  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f08038d.
07-20 20:22:58.870  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f050051.
07-20 20:22:58.870  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f1408c0.
07-20 20:22:58.871  1298  2731 E ShortcutService: Ignoring excessive intent tag.
07-20 20:22:58.871  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f08038c.
07-20 20:22:58.871  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f1412f7.
07-20 20:22:58.900  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f0f0000.
07-20 20:22:58.900  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f11012c.
07-20 20:22:58.900  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f11012c.
07-20 20:22:58.928  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f080095.
07-20 20:22:58.928  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f110287.
07-20 20:22:58.932  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f080493.
07-20 20:22:58.932  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f140ae4.
07-20 20:22:58.938  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f0700d1.
07-20 20:22:58.938  1298  2731 E system_server: No package ID 7f found for resource ID 0x7f12009a.
07-20 20:22:58.953  2334  2361 E SingleInstanceRemoteListener: Failed remote call on null listener
07-20 20:22:59.003  1986  1986 E CarrierKeyDownloadManager [0]: handleAlarmOrConfigChange ActiveSubscriptionInfoList = 2
07-20 20:22:59.008  1986  1986 E CarrierKeyDownloadManager [1]: handleAlarmOrConfigChange ActiveSubscriptionInfoList = 2
07-20 20:22:59.028  2169  2815 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:22:59.034  2334  2361 E SingleInstanceRemoteListener: Failed remote call on null listener
07-20 20:22:59.077  1298  1321 E Parcel  : Reading a NULL string not supported here.
07-20 20:22:59.077  1298  1321 E ARProxy : Unknown descriptor: 
07-20 20:22:59.114  2169  2816 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:22:59.147  1298  2732 E AppSearchIcing: document-store.cc:518: Invalid header crc for /data/system_ce/0/appsearch/icing/document_dir/uri_mapper/persistent_hash_map.kFailed to initialize KeyMapper
07-20 20:22:59.171  2169  2815 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:22:59.192  2334  2361 E SingleInstanceRemoteListener: Failed remote call on null listener
07-20 20:22:59.240   829  3016 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 2, gainDevice 2, return
07-20 20:22:59.250   829  3016 E AudioALSADeviceConfigManager: Error: ApplyDeviceTurnonSequenceByName  cltname.string () = TFA98XX Profile cltvalue.string () = stereo
07-20 20:22:59.250   829  3016 E AudioALSADeviceConfigManager: AUD_ASSERT(false) fail: "vendor/mediatek/proprietary/hardware/audio/common/V3/aud_drv/AudioALSADeviceConfigManager.cpp", 563L
07-20 20:22:59.250   829  3016 E AEE_LIBAEEV: shell: cant create socket with aed: Connection refused
07-20 20:22:59.284  2877  2877 E CardEmulationManager: adapter is null, returning
07-20 20:22:59.304   829  3016 E AudioSmartPaController: Error: MUSIC_48000 invalid value, ret = -22
07-20 20:22:59.304  2169  2815 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:22:59.367  2877  3067 E libnfc_nci: Unknown Mode!
07-20 20:22:59.376  1298  2291 E BiometricScheduler: Current operation is null,no need to start watchdog
07-20 20:22:59.377  1298  1937 E libMEOW : plugin: [failed].
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: Unable to schedule fully persist job for 0
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: java.lang.IllegalArgumentException: Tried to schedule job for non-existent component: ComponentInfo{android/com.android.server.appsearch.AppSearchMaintenanceService}
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: 	at com.android.server.job.JobSchedulerService$JobSchedulerStub.enforceValidJobRequest(go/retraceme b1d093d1655900eef0c7d61aac3b993c7754cf145d84d42fe519b758a20a8e8f:194)
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: 	at com.android.server.job.JobSchedulerService$JobSchedulerStub.schedule(go/retraceme b1d093d1655900eef0c7d61aac3b993c7754cf145d84d42fe519b758a20a8e8f:13)
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: 	at android.app.JobSchedulerImpl.schedule(JobSchedulerImpl.java:89)
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: 	at com.android.server.appsearch.AppSearchMaintenanceService.scheduleFullyPersistJob(AppSearchMaintenanceService.java:91)
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: 	at com.android.server.appsearch.AppSearchManagerService.lambda$onUserUnlocking$1(AppSearchManagerService.java:374)
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: 	at com.android.server.appsearch.AppSearchManagerService.$r8$lambda$7li-QUZvrsDk5ZvsFzpgCXuGV7s(AppSearchManagerService.java:0)
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: 	at com.android.server.appsearch.AppSearchManagerService$$ExternalSyntheticLambda3.run(R8$$SyntheticClass:0)
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
07-20 20:22:59.389  1298  2732 E AppSearchManagerService: 	at java.lang.Thread.run(Thread.java:1119)
07-20 20:22:59.433  2348  2348 E NxpFwDnld: I2C status read not succeeded. Default value : 0
07-20 20:22:59.436  2348  2348 E NxpFwDnld: Failed to load FW binary image file!!!
07-20 20:22:59.436  2348  2348 E NxpFwDnld: Image extraction Failed - invalid imginfo or imginfolen!!
07-20 20:22:59.436  2348  2348 E NxpFwDnld: Error loading libpn54x_fw !!
07-20 20:22:59.436  2348  2348 E NxpHal  : Image information extraction Failed!!
07-20 20:22:59.436  2348  2348 E NxpHal  : Wrong FW Version >>> Firmware download not allowed
07-20 20:22:59.439  2169  2169 E RecyclerView: Cannot scroll to position a LayoutManager set. Call setLayoutManager with a non-null argument.
07-20 20:22:59.441  2169  2169 E RecyclerView: Cannot scroll to position a LayoutManager set. Call setLayoutManager with a non-null argument.
07-20 20:22:59.555  2169  2816 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:22:59.608   886  1186 E ion     : ioctl c0044901 failed with code -1: Invalid argument
07-20 20:22:59.642  2966  3147 E FuseDaemon: Leveldb setup is missing for: internal
07-20 20:22:59.666  2966  3147 E FuseDaemon: Leveldb setup is missing for: external_primary
07-20 20:22:59.678  2966  3147 E FuseDaemon: Leveldb setup is missing for: ownership
07-20 20:22:59.757  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:22:59.759  1298  1687 E AppOps  : attributionTag VCN not declared in manifest of android
07-20 20:22:59.762  2169  2815 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:22:59.841  1298  3252 E ArtService: Failed to load pre-reboot stats
07-20 20:22:59.841  1298  3252 E ArtService: java.io.FileNotFoundException: /data/system/pre-reboot-stats.pb: open failed: ENOENT (No such file or directory)
07-20 20:22:59.841  1298  3252 E ArtService: 	at libcore.io.IoBridge.open(IoBridge.java:583)
07-20 20:22:59.841  1298  3252 E ArtService: 	at java.io.FileInputStream.<init>(FileInputStream.java:179)
07-20 20:22:59.841  1298  3252 E ArtService: 	at java.io.FileInputStream.<init>(FileInputStream.java:133)
07-20 20:22:59.841  1298  3252 E ArtService: 	at com.android.server.art.prereboot.PreRebootStatsReporter.load(PreRebootStatsReporter.java:308)
07-20 20:22:59.841  1298  3252 E ArtService: 	at com.android.server.art.prereboot.PreRebootStatsReporter.-$$Nest$mload(PreRebootStatsReporter.java:0)
07-20 20:22:59.841  1298  3252 E ArtService: 	at com.android.server.art.prereboot.PreRebootStatsReporter$AfterRebootSession.report(PreRebootStatsReporter.java:202)
07-20 20:22:59.841  1298  3252 E ArtService: 	at com.android.server.art.prereboot.PreRebootStatsReporter$AfterRebootSession$$ExternalSyntheticLambda2.run(R8$$SyntheticClass:0)
07-20 20:22:59.841  1298  3252 E ArtService: 	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1780)
07-20 20:22:59.841  1298  3252 E ArtService: 	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1772)
07-20 20:22:59.841  1298  3252 E ArtService: 	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:391)
07-20 20:22:59.841  1298  3252 E ArtService: 	at java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1322)
07-20 20:22:59.841  1298  3252 E ArtService: 	at java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1858)
07-20 20:22:59.841  1298  3252 E ArtService: 	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1823)
07-20 20:22:59.841  1298  3252 E ArtService: 	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
07-20 20:22:59.841  1298  3252 E ArtService: Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
07-20 20:22:59.841  1298  3252 E ArtService: 	at libcore.io.Linux.open(Native Method)
07-20 20:22:59.841  1298  3252 E ArtService: 	at libcore.io.ForwardingOs.open(ForwardingOs.java:563)
07-20 20:22:59.841  1298  3252 E ArtService: 	at libcore.io.BlockGuardOs.open(BlockGuardOs.java:274)
07-20 20:22:59.841  1298  3252 E ArtService: 	at libcore.io.IoBridge.open(IoBridge.java:568)
07-20 20:22:59.841  1298  3252 E ArtService: 	... 13 more
07-20 20:22:59.851  2290  2316 E WorkSourceUtil: Could not find package: gms_cast_prober
07-20 20:22:59.853  2290  2316 E WorkSourceUtil: Could not find package: gms_cast_prober
07-20 20:22:59.854  2290  2316 E WorkSourceUtil: Could not find package: gms_cast_prober
07-20 20:22:59.854  2290  2316 E WorkSourceUtil: Could not find package: gms_cast_prober
07-20 20:22:59.854  2290  2316 E WorkSourceUtil: Could not find package: gms_cast_prober
07-20 20:22:59.855  2290  2316 E WorkSourceUtil: Could not find package: gms_cast_prober
07-20 20:22:59.855  2290  2316 E WorkSourceUtil: Could not find package: gms_cast_prober
07-20 20:22:59.855  2290  2316 E WorkSourceUtil: Could not find package: gms_cast_prober
07-20 20:22:59.896  2290  3039 E NetUtils: Unknown host exception while converting IP Address integer to InetAddress.
07-20 20:22:59.896  2290  3039 E NetUtils: Unknown host exception while converting IP Address integer to InetAddress.
07-20 20:22:59.984  2169  2816 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:22:59.999  2169  2815 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:23:00.096  3218  3310 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:23:00.199  2169  2816 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:23:00.242   819   822 E statsd  : Stats puller failed for tag: 10038 at ************
07-20 20:23:00.255   819   822 E statsd  : Stats puller failed for tag: 10038 at ************
07-20 20:23:00.258   819   822 E statsd  : Stats puller failed for tag: 10011 at ************
07-20 20:23:00.304  2290  2290 E GmsCoreLogger: Missing account for FACET_USAGE_GAIA_UNSAMPLED [CONTEXT service_id=299 ]
07-20 20:23:00.366  2290  2290 E GmsCoreLogger: Missing account for FACET_USAGE_GAIA_UNSAMPLED [CONTEXT service_id=299 ]
07-20 20:23:00.369   632  1270 E android.system.suspend-service: Error opening kernel wakelock stats for: wakeup169 (../../devices/platform/********.vpu_core1/wakeup/wakeup169): Permission denied
07-20 20:23:00.461  2169  2816 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:23:00.544  2169  2815 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:23:00.735   819   822 E statsd  : Stats puller failed for tag: 10011 at ************
07-20 20:23:00.738   819   822 E statsd  : Gauge Stats puller failed for tag: 10086 at ************
07-20 20:23:00.739   819   822 E statsd  : Gauge Stats puller failed for tag: 10087 at ************
07-20 20:23:00.826  2290  3040 E ggcy    : RuntimeException while executing runnable ggdn{ajmr@d2209ce} with executor MoreExecutors.directExecutor()
07-20 20:23:00.826  2290  3040 E ggcy    : java.lang.NullPointerException: Attempt to get length of null array
07-20 20:23:00.826  2290  3040 E ggcy    : 	at android.util.Base64.encode(Base64.java:503)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at android.util.Base64.encodeToString(Base64.java:465)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at com.google.android.gms.auth.proximity.RemoteDevice.a(:com.google.android.gms@252635035@25.26.35 (260400-783060121):3)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at ajmr.b(:com.google.android.gms@252635035@25.26.35 (260400-783060121):3)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at ggdn.run(:com.google.android.gms@252635035@25.26.35 (260400-783060121):29)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at ggcu.execute(:com.google.android.gms@252635035@25.26.35 (260400-783060121):1)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at ggcy.c(:com.google.android.gms@252635035@25.26.35 (260400-783060121):1)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at ggcy.b(:com.google.android.gms@252635035@25.26.35 (260400-783060121):32)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at azpk.done(:com.google.android.gms@252635035@25.26.35 (260400-783060121):6)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at java.util.concurrent.FutureTask.finishCompletion(FutureTask.java:434)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at java.util.concurrent.FutureTask.set(FutureTask.java:285)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at java.util.concurrent.FutureTask.run(FutureTask.java:325)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at azpf.c(:com.google.android.gms@252635035@25.26.35 (260400-783060121):50)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at azpf.run(:com.google.android.gms@252635035@25.26.35 (260400-783060121):70)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at azuw.run(:com.google.android.gms@252635035@25.26.35 (260400-783060121):8)
07-20 20:23:00.826  2290  3040 E ggcy    : 	at java.lang.Thread.run(Thread.java:1119)
07-20 20:23:00.834  2169  2816 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:23:00.894   819   822 E statsd  : Gauge Stats puller failed for tag: 10028 at ************
07-20 20:23:00.945   819   822 E statsd  : Gauge Stats puller failed for tag: 10091 at ************
07-20 20:23:01.042  1298  1937 E BackupTransportManager: [UserID:0] Transport com.google.android.gms/.backup.BackupTransportService not registered tried to change description
07-20 20:23:01.046  2506  3373 E CastAuth: [CastAuthModuleInitIntent] Feature is not available, reason = CastAuth feature is disabled on this 3p device.. [CONTEXT service_id=360 ]
07-20 20:23:01.050   632  1270 E android.system.suspend-service: Error opening kernel wakelock stats for: wakeup169 (../../devices/platform/********.vpu_core1/wakeup/wakeup169): Permission denied
07-20 20:23:01.077  2506  2841 E JavaBinder: !!! FAILED BINDER TRANSACTION !!!  (parcel size = 1056768)
07-20 20:23:01.197   819   822 E statsd  : Gauge Stats puller failed for tag: 10205 at ************
07-20 20:23:01.270   819   822 E statsd  : Gauge Stats puller failed for tag: 10077 at ************
07-20 20:23:01.271   819   822 E statsd  : Gauge Stats puller failed for tag: 10076 at ************
07-20 20:23:01.431   819   822 E statsd  : Gauge Stats puller failed for tag: 10191 at ************
07-20 20:23:01.432  1298  2327 E system_server: DMA-BUF heaps not supported, read ION heap total instead.
07-20 20:23:01.434  1298  2327 E system_server: Unable to access: /sys/kernel/dmabuf/buffers: No such file or directory
07-20 20:23:01.444   819   822 E statsd  : Gauge Stats puller failed for tag: 10239 at ************
07-20 20:23:01.486   819   822 E statsd  : Gauge Stats puller failed for tag: 10089 at ************
07-20 20:23:01.486   819   822 E statsd  : Gauge Stats puller failed for tag: 150004 at ************
07-20 20:23:01.486   819   822 E statsd  : Gauge Stats puller failed for tag: 150001 at ************
07-20 20:23:01.489   819   822 E statsd  : Gauge Stats puller failed for tag: 10090 at ************
07-20 20:23:01.492   819   822 E statsd  : Gauge Stats puller failed for tag: 10222 at ************
07-20 20:23:01.492   819   822 E statsd  : Gauge Stats puller failed for tag: 10221 at ************
07-20 20:23:01.492   819   822 E statsd  : Gauge Stats puller failed for tag: 10223 at ************
07-20 20:23:01.492   819   822 E statsd  : Gauge Stats puller failed for tag: 10224 at ************
07-20 20:23:01.564   819   822 E statsd  : Gauge Stats puller failed for tag: 150001 at ************
07-20 20:23:01.564   819   822 E statsd  : Gauge Stats puller failed for tag: 150002 at ************
07-20 20:23:01.564   819   822 E statsd  : Gauge Stats puller failed for tag: 150003 at ************
07-20 20:23:01.564   819   822 E statsd  : Gauge Stats puller failed for tag: 150004 at ************
07-20 20:23:01.564   819   822 E statsd  : Gauge Stats puller failed for tag: 150005 at ************
07-20 20:23:01.564   819   822 E statsd  : Gauge Stats puller failed for tag: 150006 at ************
07-20 20:23:01.575   819   822 E statsd  : Gauge Stats puller failed for tag: 10190 at ************
07-20 20:23:01.592  2506  3371 E libMEOW : plugin: [failed].
07-20 20:23:01.676   634   634 E TlcTeeKeyMaster: Error: Cannot open file: /mnt/vendor/persist/attest_keybox.so.
07-20 20:23:01.676   634   634 E TlcTeeKeyMaster: Open attest keybox failed!
07-20 20:23:01.685   819   822 E statsd  : Gauge Stats puller failed for tag: 10093 at ************
07-20 20:23:01.686   819   822 E statsd  : Gauge Stats puller failed for tag: 10094 at ************
07-20 20:23:01.686   819   822 E statsd  : Gauge Stats puller failed for tag: 10157 at ************
07-20 20:23:01.686   819   822 E statsd  : Gauge Stats puller failed for tag: 10158 at ************
07-20 20:23:01.690  1298  2327 E system_server: Failed to read fdinfo - requires either PTRACE_MODE_READ or root depending on the device kernel
07-20 20:23:01.690  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1
07-20 20:23:01.713  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2
07-20 20:23:01.713  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3
07-20 20:23:01.713  1298  2327 E system_server: Failed to read dmabuf fd references for pid 4
07-20 20:23:01.713  1298  2327 E system_server: Failed to read dmabuf fd references for pid 5
07-20 20:23:01.713  1298  2327 E system_server: Failed to read dmabuf fd references for pid 6
07-20 20:23:01.713  1298  2327 E system_server: Failed to read dmabuf fd references for pid 7
07-20 20:23:01.713  1298  2327 E system_server: Failed to read dmabuf fd references for pid 8
07-20 20:23:01.713  1298  2327 E system_server: Failed to read dmabuf fd references for pid 9
07-20 20:23:01.713  1298  2327 E system_server: Failed to read dmabuf fd references for pid 10
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 11
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 12
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 13
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 14
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 15
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 16
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 17
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 18
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 19
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 20
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 21
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 22
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 23
07-20 20:23:01.714  1298  2327 E system_server: Failed to read dmabuf fd references for pid 24
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 25
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 26
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 27
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 28
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 29
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 30
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 31
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 32
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 33
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 34
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 35
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 36
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 37
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 38
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 39
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 40
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 41
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 42
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 43
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 44
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 45
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 46
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 47
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 48
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 49
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 50
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 51
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 52
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 53
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 54
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 55
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 56
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 57
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 58
07-20 20:23:01.715  1298  2327 E system_server: Failed to read dmabuf fd references for pid 59
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 60
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 61
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 62
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 63
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 64
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 65
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 66
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 67
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 68
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 69
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 70
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 71
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 72
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 73
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 74
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 75
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 76
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 77
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 78
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 79
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 80
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 81
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 82
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 83
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 84
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 85
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 86
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 87
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 88
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 89
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 90
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 91
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 92
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 93
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 94
07-20 20:23:01.716  1298  2327 E system_server: Failed to read dmabuf fd references for pid 95
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 96
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 97
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 98
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 99
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 100
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 101
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 102
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 103
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 104
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 105
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 106
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 107
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 108
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 109
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 110
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 111
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 112
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 113
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 114
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 115
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 116
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 117
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 118
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 119
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 120
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 121
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 122
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 123
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 124
07-20 20:23:01.717  1298  2327 E system_server: Failed to read dmabuf fd references for pid 125
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 126
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 127
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 128
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 129
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 130
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 131
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 132
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 133
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 134
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 135
07-20 20:23:01.718  1298  2327 E system_server: Failed to read dmabuf fd references for pid 136
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 137
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 138
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 139
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 141
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 143
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 144
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 145
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 146
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 147
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 148
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 149
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 150
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 151
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 152
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 173
07-20 20:23:01.719  1298  2327 E system_server: Failed to read dmabuf fd references for pid 174
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 219
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 220
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 221
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 222
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 223
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 224
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 225
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 226
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 227
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 228
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 229
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 230
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 231
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 232
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 233
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 234
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 235
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 236
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 237
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 238
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 239
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 240
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 241
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 242
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 243
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 244
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 245
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 246
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 247
07-20 20:23:01.720  1298  2327 E system_server: Failed to read dmabuf fd references for pid 248
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 249
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 250
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 251
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 252
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 253
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 254
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 255
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 256
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 257
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 258
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 259
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 260
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 261
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 262
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 263
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 264
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 265
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 266
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 267
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 268
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 269
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 270
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 271
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 272
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 273
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 274
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 275
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 276
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 277
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 278
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 279
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 280
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 281
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 282
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 283
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 284
07-20 20:23:01.721  1298  2327 E system_server: Failed to read dmabuf fd references for pid 285
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 286
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 287
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 288
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 289
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 290
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 291
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 292
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 293
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 294
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 296
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 297
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 298
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 299
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 300
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 301
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 302
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 303
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 305
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 306
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 307
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 308
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 309
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 310
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 311
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 312
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 313
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 314
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 315
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 316
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 317
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 318
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 319
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 320
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 321
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 322
07-20 20:23:01.722  1298  2327 E system_server: Failed to read dmabuf fd references for pid 323
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 324
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 325
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 326
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 327
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 328
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 329
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 330
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 331
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 332
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 333
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 334
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 335
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 336
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 337
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 338
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 339
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 340
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 341
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 342
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 343
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 344
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 345
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 346
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 347
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 348
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 349
07-20 20:23:01.723  1298  2327 E system_server: Failed to read dmabuf fd references for pid 350
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 351
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 352
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 353
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 354
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 355
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 356
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 357
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 358
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 359
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 360
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 361
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 362
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 363
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 364
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 365
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 366
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 367
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 368
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 369
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 370
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 371
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 372
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 373
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 374
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 375
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 376
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 377
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 378
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 379
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 380
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 381
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 382
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 383
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 384
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 385
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 386
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 387
07-20 20:23:01.724  1298  2327 E system_server: Failed to read dmabuf fd references for pid 389
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 390
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 391
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 392
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 393
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 395
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 396
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 397
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 398
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 399
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 400
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 401
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 402
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 403
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 404
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 405
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 406
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 407
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 408
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 409
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 410
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 411
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 412
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 413
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 414
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 415
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 416
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 417
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 418
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 419
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 420
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 421
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 422
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 423
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 424
07-20 20:23:01.725  1298  2327 E system_server: Failed to read dmabuf fd references for pid 425
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 426
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 427
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 428
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 429
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 430
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 431
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 432
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 433
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 434
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 435
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 436
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 437
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 438
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 440
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 441
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 442
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 443
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 444
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 445
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 446
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 447
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 448
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 449
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 450
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 451
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 453
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 455
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 456
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 457
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 458
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 459
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 460
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 461
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 462
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 464
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 468
07-20 20:23:01.726  1298  2327 E system_server: Failed to read dmabuf fd references for pid 469
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 470
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 471
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 472
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 473
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 474
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 475
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 477
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 478
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 479
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 482
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 483
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 484
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 485
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 486
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 487
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 488
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 489
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 490
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 491
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 492
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 493
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 494
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 498
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 499
07-20 20:23:01.727  1298  2327 E system_server: Failed to read dmabuf fd references for pid 504
07-20 20:23:01.728  1298  2327 E system_server: Failed to read dmabuf fd references for pid 505
07-20 20:23:01.728  1298  2327 E system_server: Failed to read dmabuf fd references for pid 506
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 517
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 518
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 519
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 520
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 523
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 524
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 525
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 528
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 531
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 532
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 533
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 534
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 535
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 536
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 537
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 538
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 540
07-20 20:23:01.729  1298  2327 E system_server: Failed to read dmabuf fd references for pid 543
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 544
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 545
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 546
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 548
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 552
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 553
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 554
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 555
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 556
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 557
07-20 20:23:01.730  1298  2327 E system_server: Failed to read dmabuf fd references for pid 560
07-20 20:23:01.731  1298  2327 E system_server: Failed to read dmabuf fd references for pid 561
07-20 20:23:01.731  1298  2327 E system_server: Failed to read dmabuf fd references for pid 563
07-20 20:23:01.731  1298  2327 E system_server: Failed to read dmabuf fd references for pid 564
07-20 20:23:01.731  1298  2327 E system_server: Failed to read dmabuf fd references for pid 576
07-20 20:23:01.732  1298  2327 E system_server: Failed to read dmabuf fd references for pid 577
07-20 20:23:01.735  1298  2327 E system_server: Failed to read dmabuf fd references for pid 581
07-20 20:23:01.735  1298  2327 E system_server: Failed to read dmabuf fd references for pid 590
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 594
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 595
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 599
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 603
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 604
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 608
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 609
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 613
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 614
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 618
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 619
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 627
07-20 20:23:01.736  1298  2327 E system_server: Failed to read dmabuf fd references for pid 628
07-20 20:23:01.738  1298  2327 E system_server: Failed to read dmabuf fd references for pid 633
07-20 20:23:01.740  1298  2327 E system_server: Failed to read dmabuf fd references for pid 650
07-20 20:23:01.740  1298  2327 E system_server: Failed to read dmabuf fd references for pid 651
07-20 20:23:01.740  1298  2327 E system_server: Failed to read dmabuf fd references for pid 652
07-20 20:23:01.740  1298  2327 E system_server: Failed to read dmabuf fd references for pid 653
07-20 20:23:01.741  1298  2327 E system_server: Failed to read dmabuf fd references for pid 663
07-20 20:23:01.741  1298  2327 E system_server: Failed to read dmabuf fd references for pid 704
07-20 20:23:01.741  1298  2327 E system_server: Failed to read dmabuf fd references for pid 705
07-20 20:23:01.741  1298  2327 E system_server: Failed to read dmabuf fd references for pid 706
07-20 20:23:01.741  1298  2327 E system_server: Failed to read dmabuf fd references for pid 707
07-20 20:23:01.741  1298  2327 E system_server: Failed to read dmabuf fd references for pid 708
07-20 20:23:01.741  1298  2327 E system_server: Failed to read dmabuf fd references for pid 709
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 710
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 711
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 712
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 713
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 714
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 715
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 716
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 717
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 718
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 719
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 720
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 721
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 722
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 723
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 724
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 725
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 726
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 727
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 728
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 729
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 730
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 731
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 732
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 733
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 734
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 735
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 736
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 737
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 738
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 739
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 740
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 741
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 742
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 743
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 744
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 745
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 746
07-20 20:23:01.742  1298  2327 E system_server: Failed to read dmabuf fd references for pid 747
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 748
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 749
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 750
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 751
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 752
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 753
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 754
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 755
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 756
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 757
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 758
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 759
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 760
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 761
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 762
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 763
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 764
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 765
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 766
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 767
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 768
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 769
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 770
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 771
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 772
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 773
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 774
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 775
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 776
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 777
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 778
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 779
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 780
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 781
07-20 20:23:01.743  1298  2327 E system_server: Failed to read dmabuf fd references for pid 782
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 783
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 784
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 785
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 786
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 787
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 788
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 789
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 790
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 791
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 792
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 793
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 794
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 795
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 796
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 797
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 813
07-20 20:23:01.744  1298  2327 E system_server: Failed to read dmabuf fd references for pid 818
07-20 20:23:01.745  1298  2327 E system_server: Failed to read dmabuf fd references for pid 819
07-20 20:23:01.746  1298  2327 E system_server: Failed to read dmabuf fd references for pid 820
07-20 20:23:01.751  1298  2327 E system_server: Failed to read dmabuf fd references for pid 821
07-20 20:23:01.757  1298  2327 E system_server: Failed to read dmabuf fd references for pid 829
07-20 20:23:01.758  2290  2303 E UsageReportingOptionsSt: INTERNAL_ERROR: setOptInOption should not be called while user is locked. [CONTEXT service_id=41 ]
07-20 20:23:01.759  1298  2327 E system_server: Failed to read dmabuf fd references for pid 830
07-20 20:23:01.760  1298  2327 E system_server: Failed to read dmabuf fd references for pid 831
07-20 20:23:01.760  1298  2327 E system_server: Failed to read dmabuf fd references for pid 832
07-20 20:23:01.761  1298  2327 E system_server: Failed to read dmabuf fd references for pid 833
07-20 20:23:01.774  1298  2327 E system_server: Failed to read dmabuf fd references for pid 839
07-20 20:23:01.776  1298  2327 E system_server: Failed to read dmabuf fd references for pid 840
07-20 20:23:01.777  1298  2327 E system_server: Failed to read dmabuf fd references for pid 841
07-20 20:23:01.779  1298  2327 E system_server: Failed to read dmabuf fd references for pid 845
07-20 20:23:01.782  1298  2327 E system_server: Failed to read dmabuf fd references for pid 853
07-20 20:23:01.783  1298  2327 E system_server: Failed to read dmabuf fd references for pid 855
07-20 20:23:01.787  1298  2327 E system_server: Failed to read dmabuf fd references for pid 864
07-20 20:23:01.788  1298  2327 E system_server: Failed to read dmabuf fd references for pid 871
07-20 20:23:01.791  1298  2327 E system_server: Failed to read dmabuf fd references for pid 874
07-20 20:23:01.792  1298  2327 E system_server: Failed to read dmabuf fd references for pid 882
07-20 20:23:01.803  1298  2327 E system_server: Failed to read dmabuf fd references for pid 896
07-20 20:23:01.804  1298  2327 E system_server: Failed to read dmabuf fd references for pid 912
07-20 20:23:01.804  1298  2327 E system_server: Failed to read dmabuf fd references for pid 917
07-20 20:23:01.804  1298  2327 E system_server: Failed to read dmabuf fd references for pid 921
07-20 20:23:01.804  1298  2327 E system_server: Failed to read dmabuf fd references for pid 922
07-20 20:23:01.804  1298  2327 E system_server: Failed to read dmabuf fd references for pid 923
07-20 20:23:01.804  1298  2327 E system_server: Failed to read dmabuf fd references for pid 924
07-20 20:23:01.804  1298  2327 E system_server: Failed to read dmabuf fd references for pid 925
07-20 20:23:01.804  1298  2327 E system_server: Failed to read dmabuf fd references for pid 928
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 931
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 932
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 933
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 934
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 935
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 936
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 937
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 938
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 943
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 944
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 948
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 952
07-20 20:23:01.805  1298  2327 E system_server: Failed to read dmabuf fd references for pid 976
07-20 20:23:01.806  1298  2327 E system_server: Failed to read dmabuf fd references for pid 982
07-20 20:23:01.807  1298  2327 E system_server: Failed to read dmabuf fd references for pid 983
07-20 20:23:01.809  1298  2327 E system_server: Failed to read dmabuf fd references for pid 991
07-20 20:23:01.810  3552  3762 E DrmHalAidl: uuid=[edef8ba979d64ace a3c827dcd51d21ed] No supported hal instance found
07-20 20:23:01.810  1298  2327 E system_server: Failed to read dmabuf fd references for pid 992
07-20 20:23:01.813  1298  2327 E system_server: Failed to read dmabuf fd references for pid 995
07-20 20:23:01.813  1298  2327 E system_server: Failed to read dmabuf fd references for pid 996
07-20 20:23:01.815  1298  2327 E system_server: Failed to read dmabuf fd references for pid 997
07-20 20:23:01.817  1298  2327 E system_server: Failed to read dmabuf fd references for pid 998
07-20 20:23:01.818  1298  2327 E system_server: Failed to read dmabuf fd references for pid 999
07-20 20:23:01.821  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1000
07-20 20:23:01.822  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1001
07-20 20:23:01.822  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1003
07-20 20:23:01.824  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1021
07-20 20:23:01.831  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1033
07-20 20:23:01.834  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1035
07-20 20:23:01.835  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1036
07-20 20:23:01.835  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1037
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1047
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1049
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1152
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1159
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1160
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1162
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1163
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1164
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1165
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1166
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1167
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1168
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1169
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1170
07-20 20:23:01.842  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1171
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1176
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1177
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1178
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1179
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1203
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1204
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1205
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1206
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1223
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1273
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1297
07-20 20:23:01.843  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1298
07-20 20:23:01.851   633  3784 E keystore2: system/security/keystore2/src/gc.rs:139 - Error trying to delete blob entry. system/security/keystore2/src/gc.rs:129: Trying to invalidate key.
07-20 20:23:01.851   633  3784 E keystore2: 
07-20 20:23:01.851   633  3784 E keystore2: Caused by:
07-20 20:23:01.851   633  3784 E keystore2:     0: system/security/keystore2/src/globals.rs:178: Trying to invalidate key blob.
07-20 20:23:01.851   633  3784 E keystore2:     1: Error::Km(r#UNIMPLEMENTED)
07-20 20:23:01.854  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1304
07-20 20:23:01.854  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1338
07-20 20:23:01.854  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1356
07-20 20:23:01.855  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1358
07-20 20:23:01.855  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1359
07-20 20:23:01.855  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1392
07-20 20:23:01.857  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1496
07-20 20:23:01.857  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1497
07-20 20:23:01.857  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1498
07-20 20:23:01.857  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1499
07-20 20:23:01.857  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1500
07-20 20:23:01.857  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1501
07-20 20:23:01.857  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1502
07-20 20:23:01.857  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1503
07-20 20:23:01.857  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1504
07-20 20:23:01.857  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1505
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1506
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1507
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1508
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1509
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1510
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1511
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1512
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1513
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1514
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1515
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1516
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1517
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1518
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1519
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1520
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1521
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1522
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1523
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1524
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1525
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1527
07-20 20:23:01.858  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1588
07-20 20:23:01.859  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1741
07-20 20:23:01.859  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1745
07-20 20:23:01.860  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1782
07-20 20:23:01.866  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1895
07-20 20:23:01.871  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1910
07-20 20:23:01.877  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1950
07-20 20:23:01.882  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1968
07-20 20:23:01.888  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1986
07-20 20:23:01.896  1298  2327 E system_server: Failed to read dmabuf fd references for pid 1993
07-20 20:23:01.900   833   833 E TeeSysClient: TEEC_InvokeCommand returned ??? (rc 0xa) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/ClientLib/src/native_interface.cpp:364]
07-20 20:23:01.902  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2060
07-20 20:23:01.903  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2104
07-20 20:23:01.909  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2117
07-20 20:23:01.916  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2169
07-20 20:23:01.924  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2275
07-20 20:23:01.925  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2290
07-20 20:23:01.928   833   876 E WVCdm   : App requested unknown string property provisioningModel
07-20 20:23:01.932  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2334
07-20 20:23:01.934  3552  3762 E RkpdWidevine: Something went wrong. Will not provision widevine certificates.
07-20 20:23:01.934  3552  3762 E RkpdWidevine: java.lang.IllegalArgumentException: Failed to get property: ERROR_DRM_CANNOT_HANDLE
07-20 20:23:01.934  3552  3762 E RkpdWidevine: cdm err: 0, oem err: 0, ctx: 0
07-20 20:23:01.934  3552  3762 E RkpdWidevine: ============================== Beginning of DRM Plugin Log ==============================
07-20 20:23:01.934  3552  3762 E RkpdWidevine:   07-20 20:23:01.808 I found instance=widevine version=android.hardware.drm@1.4::IDrmFactory
07-20 20:23:01.934  3552  3762 E RkpdWidevine:   07-20 20:23:01.810 E uuid=[edef8ba979d64ace a3c827dcd51d21ed] No supported hal instance found
07-20 20:23:01.934  3552  3762 E RkpdWidevine:   07-20 20:23:01.815 I [oemcrypto_adapter_dynamic.cpp(834):Initialize] Level 3 Build Info (v16): OEMCrypto Level3 Code 22594 May 28 2021 16:59:07
07-20 20:23:01.934  3552  3762 E RkpdWidevine:   07-20 20:23:01.833 I [(0):] Level3 Library 22594 May 28 2021 16:59:07
07-20 20:23:01.934  3552  3762 E RkpdWidevine:   07-20 20:23:01.852 I [oemcrypto_adapter_dynamic.cpp(848):Initialize] L3 Initialized. Trying L1.
07-20 20:23:01.934  3552  3762 E RkpdWidevine:   07-20 20:23:01.926 W [oemcrypto_adapter_dynamic.cpp(1047):LoadLevel1] Could not load L1 _oecc113.
07-20 20:23:01.934  3552  3762 E RkpdWidevine:   07-20 20:23:01.926 W [oemcrypto_adapter_dynamic.cpp(1048):LoadLevel1] Could not load L1 _oecc114.
07-20 20:23:01.934  3552  3762 E RkpdWidevine:   07-20 20:23:01.926 I [oemcrypto_adapter_dynamic.cpp(870):Initialize] Level 1 Build Info (v16): OEMCrypto Level1 code with V16 on 2021/10/28
07-20 20:23:01.934  3552  3762 E RkpdWidevine: ============================== End of DRM Plugin Log ==============================
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at android.media.MediaDrm.getPropertyString(Native Method)
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at com.android.rkpdapp.provisioner.WidevineProvisioner.isWidevineProvisioningNeeded(WidevineProvisioner.java:112)
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at com.android.rkpdapp.provisioner.WidevineProvisioner.doWork(WidevineProvisioner.java:96)
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at androidx.work.Worker$startWork$1.invoke(Worker.kt:64)
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at androidx.work.Worker$startWork$1.invoke(Worker.kt:64)
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at androidx.work.WorkerKt.future$lambda$2$lambda$1(Worker.kt:100)
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at androidx.work.WorkerKt.$r8$lambda$06LNzu7McnKR6G06fSbfQ2BCegc(Worker.kt:0)
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at androidx.work.WorkerKt$$ExternalSyntheticLambda2.run(R8$$SyntheticClass:0)
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
07-20 20:23:01.934  3552  3762 E RkpdWidevine: 	at java.lang.Thread.run(Thread.java:1119)
07-20 20:23:01.941  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2346
07-20 20:23:01.945   833   833 E WVCdm   : [oemcrypto_adapter_dynamic.cpp(882):Level1Terminate] L1 Terminate
07-20 20:23:01.947  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2348
07-20 20:23:01.948  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2349
07-20 20:23:01.954  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2350
07-20 20:23:01.955  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2427
07-20 20:23:01.955  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2442
07-20 20:23:01.960  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2506
07-20 20:23:01.967  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2555
07-20 20:23:01.969  3737  3737 E TrafficStats: TrafficStats not initialized, uid=10150
07-20 20:23:01.973  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2569
07-20 20:23:01.979  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2587
07-20 20:23:01.986  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2653
07-20 20:23:01.986  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2715
07-20 20:23:01.986  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2728
07-20 20:23:01.987  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2741
07-20 20:23:01.994  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2747
07-20 20:23:02.000  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2748
07-20 20:23:02.005  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2756
07-20 20:23:02.011  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2758
07-20 20:23:02.016  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2759
07-20 20:23:02.021  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2767
07-20 20:23:02.027  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2776
07-20 20:23:02.033  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2808
07-20 20:23:02.039  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2821
07-20 20:23:02.044  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2852
07-20 20:23:02.045  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2877
07-20 20:23:02.050  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2884
07-20 20:23:02.056  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2907
07-20 20:23:02.063  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2939
07-20 20:23:02.070  1298  2327 E system_server: Failed to read dmabuf fd references for pid 2966
07-20 20:23:02.076  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3019
07-20 20:23:02.081  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3085
07-20 20:23:02.082  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3116
07-20 20:23:02.088  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3133
07-20 20:23:02.093  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3157
07-20 20:23:02.099  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3177
07-20 20:23:02.105  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3218
07-20 20:23:02.112  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3244
07-20 20:23:02.118  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3261
07-20 20:23:02.124  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3302
07-20 20:23:02.130  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3314
07-20 20:23:02.137  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3358
07-20 20:23:02.142  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3382
07-20 20:23:02.148  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3389
07-20 20:23:02.154  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3395
07-20 20:23:02.155  2877  2905 E NfcService: isObserveModeSupported: NFC must be enabled but is: 1
07-20 20:23:02.160  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3428
07-20 20:23:02.165  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3468
07-20 20:23:02.171  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3496
07-20 20:23:02.180  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3505
07-20 20:23:02.186  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3535
07-20 20:23:02.192  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3552
07-20 20:23:02.199  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3604
07-20 20:23:02.205  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3646
07-20 20:23:02.211  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3691
07-20 20:23:02.218  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3710
07-20 20:23:02.223  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3737
07-20 20:23:02.230  1298  2327 E system_server: Failed to open /proc/3754/fdinfo directory: No such file or directory
07-20 20:23:02.230  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3754
07-20 20:23:02.230  1298  2327 E system_server: Failed to open /proc/3754/maps for pid: 3754
07-20 20:23:02.230  1298  2327 E system_server: Failed to read dmabuf map references for pid 3754
07-20 20:23:02.230  1298  2327 E system_server: Failed to read dmabuf fd references for pid 3755
07-20 20:23:02.236  1298  3851 E SettingsTelemetryUtils: PEAK_REFRESH_RATE value is null
07-20 20:23:02.246   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.246   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 40. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.247   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.248   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.249   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.249   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.249   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.249   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.249   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.249   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.249   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.252   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.252   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.252   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.252   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.252   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.252   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.252   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.253   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.254   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.255   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.255   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.255   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.255   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.255   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.255   819   825 E statsd  : StateTracker error extracting state from log event 27. Missing exclusive state field.
07-20 20:23:02.262   819   822 E statsd  : Gauge Stats puller failed for tag: 10212 at 180736701933
07-20 20:23:02.263   819   822 E statsd  : Gauge Stats puller failed for tag: 10089 at 180736701933
07-20 20:23:02.263   819   822 E statsd  : Gauge Stats puller failed for tag: 10215 at 180736701933
07-20 20:23:02.263   819   822 E statsd  : Gauge Stats puller failed for tag: 10090 at 180736701933
07-20 20:23:02.263   819   822 E statsd  : Gauge Stats puller failed for tag: 10086 at 180736701933
07-20 20:23:02.263   819   822 E statsd  : Gauge Stats puller failed for tag: 10087 at 180736701933
07-20 20:23:02.263   819   822 E statsd  : Gauge Stats puller failed for tag: 10157 at 180736701933
07-20 20:23:02.263   819   822 E statsd  : Gauge Stats puller failed for tag: 10158 at 180736701933
07-20 20:23:02.263   819   822 E statsd  : Gauge Stats puller failed for tag: 10076 at 180736701933
07-20 20:23:02.264   819   822 E statsd  : Gauge Stats puller failed for tag: 10182 at 180736701933
07-20 20:23:02.264   819   822 E statsd  : Gauge Stats puller failed for tag: 10184 at 180736701933
07-20 20:23:02.264   819   822 E statsd  : Gauge Stats puller failed for tag: 10185 at 180736701933
07-20 20:23:02.265   819   822 E statsd  : Gauge Stats puller failed for tag: 10186 at 180736701933
07-20 20:23:02.265   819   822 E statsd  : Gauge Stats puller failed for tag: 10183 at 180736701933
07-20 20:23:02.265   819   822 E statsd  : Gauge Stats puller failed for tag: 10187 at 180736701933
07-20 20:23:02.265   819   822 E statsd  : Gauge Stats puller failed for tag: 10077 at 180736701933
07-20 20:23:02.266   819   822 E statsd  : Gauge Stats puller failed for tag: 10211 at 180736701933
07-20 20:23:02.266   819   822 E statsd  : Gauge Stats puller failed for tag: 10219 at 180736701933
07-20 20:23:02.266   819   822 E statsd  : Gauge Stats puller failed for tag: 10214 at 180736701933
07-20 20:23:02.266   819   822 E statsd  : Gauge Stats puller failed for tag: 10091 at 180736701933
07-20 20:23:02.320  2290  3034 E WorkSourceUtil: Could not find package: com.google.android.gms.westworld
07-20 20:23:02.324  2290  3034 E WorkSourceUtil: Could not find package: com.google.android.gms.westworld
07-20 20:23:02.457  3505  3607 E libMEOW : plugin: [failed].
07-20 20:23:02.484   829  3002 E adsp_service: adsp_deregister_feature() ioctl fail! ret = -1, errno: 22
07-20 20:23:02.674  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:23:02.676  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:23:02.719  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:23:02.720  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:23:02.755  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:23:02.756  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:23:02.765  1298  3972 E RoleControllerServiceImpl: Default/fallback role holder package doesn't qualify for the role, package: com.android.devicelockcontroller, role: android.app.role.SYSTEM_FINANCED_DEVICE_CONTROLLER
07-20 20:23:02.776  1986  1986 E SmsApplication: com.android.messaging lost android:receive_wap_push:  (fixing)
07-20 20:23:02.776  1986  1986 E SmsApplication: com.android.messaging lost android:read_cell_broadcasts:  (fixing)
07-20 20:23:02.956  1986  1986 E PhoneInterfaceManager: queryModemActivityInfo: invalid response
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: Cannot write token, exception in Context lookup [CONTEXT service_id=130 ]
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: java.io.FileNotFoundException: /data/user/0/com.google.android.gsf/files/security_token: open failed: ENOENT (No such file or directory)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at libcore.io.IoBridge.open(IoBridge.java:583)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at java.io.FileOutputStream.<init>(FileOutputStream.java:259)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at android.app.ContextImpl.openFileOutput(ContextImpl.java:806)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at atyi.a(:com.google.android.gms@252635035@25.26.35 (260400-783060121):1738)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at atzs.a(:com.google.android.gms@252635035@25.26.35 (260400-783060121):834)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at com.google.android.gms.checkin.CheckinIntentOperation.onHandleIntent(:com.google.android.gms@252635035@25.26.35 (260400-783060121):30)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at com.google.android.chimera.IntentOperation.onHandleIntent(:com.google.android.gms@252635035@25.26.35 (260400-783060121):2)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at aumk.onHandleIntent(:com.google.android.gms@252635035@25.26.35 (260400-783060121):8)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at vyz.run(:com.google.android.gms@252635035@25.26.35 (260400-783060121):64)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at vyy.run(:com.google.android.gms@252635035@25.26.35 (260400-783060121):132)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at fxzy.run(:com.google.android.gms@252635035@25.26.35 (260400-783060121):21)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at java.lang.Thread.run(Thread.java:1119)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at libcore.io.Linux.open(Native Method)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at libcore.io.ForwardingOs.open(ForwardingOs.java:563)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at libcore.io.BlockGuardOs.open(BlockGuardOs.java:274)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at libcore.io.ForwardingOs.open(ForwardingOs.java:563)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at android.app.ActivityThread$AndroidOs.open(ActivityThread.java:8855)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	at libcore.io.IoBridge.open(IoBridge.java:568)
07-20 20:23:03.744  2506  2827 E GoogleSettingsUtils: 	... 13 more
07-20 20:23:03.752  2506  2903 E SharedPreferencesImpl: Couldn't create directory for SharedPreferences file /data/user/0/com.google.android.gsf/shared_prefs/CheckinService.xml
07-20 20:23:03.778   633   633 E SELinux : avc:  denied  { gen_unique_id } for  scontext=u:r:priv_app:s0:c512,c768 tcontext=u:r:keystore:s0 tclass=keystore2_key permissive=0
07-20 20:23:03.779   633   633 E keystore2: system/security/keystore2/src/error.rs:183 - system/security/keystore2/src/security_level.rs:574: Trying to get aaid.
07-20 20:23:03.779   633   633 E keystore2: 
07-20 20:23:03.779   633   633 E keystore2: Caused by:
07-20 20:23:03.779   633   633 E keystore2:     0: system/security/keystore2/src/security_level.rs:482: Caller does not have the permission to generate a unique ID
07-20 20:23:03.779   633   633 E keystore2:     1: Error::Rc(r#PERMISSION_DENIED)
07-20 20:23:03.784   633   641 E SELinux : avc:  denied  { gen_unique_id } for  scontext=u:r:priv_app:s0:c512,c768 tcontext=u:r:keystore:s0 tclass=keystore2_key permissive=0
07-20 20:23:03.784   633   641 E keystore2: system/security/keystore2/src/error.rs:183 - system/security/keystore2/src/security_level.rs:574: Trying to get aaid.
07-20 20:23:03.784   633   641 E keystore2: 
07-20 20:23:03.784   633   641 E keystore2: Caused by:
07-20 20:23:03.784   633   641 E keystore2:     0: system/security/keystore2/src/security_level.rs:482: Caller does not have the permission to generate a unique ID
07-20 20:23:03.784   633   641 E keystore2:     1: Error::Rc(r#PERMISSION_DENIED)
07-20 20:23:04.199  3395  3475 E libMEOW : plugin: [failed].
07-20 20:23:04.699   634   634 E TlcTeeKeyMaster: Error: Cannot open file: /mnt/vendor/persist/attest_keybox.so.
07-20 20:23:04.700   634   634 E TlcTeeKeyMaster: Open attest keybox failed!
07-20 20:23:04.710   633  3784 E keystore2: system/security/keystore2/src/gc.rs:139 - Error trying to delete blob entry. system/security/keystore2/src/gc.rs:129: Trying to invalidate key.
07-20 20:23:04.710   633  3784 E keystore2: 
07-20 20:23:04.710   633  3784 E keystore2: Caused by:
07-20 20:23:04.710   633  3784 E keystore2:     0: system/security/keystore2/src/globals.rs:178: Trying to invalidate key blob.
07-20 20:23:04.710   633  3784 E keystore2:     1: Error::Km(r#UNIMPLEMENTED)
07-20 20:23:31.817   829  3016 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 2, gainDevice 2, return
07-20 20:23:31.826   829  3016 E AudioALSADeviceConfigManager: Error: ApplyDeviceTurnonSequenceByName  cltname.string () = TFA98XX Profile cltvalue.string () = stereo
07-20 20:23:31.826   829  3016 E AudioALSADeviceConfigManager: AUD_ASSERT(false) fail: "vendor/mediatek/proprietary/hardware/audio/common/V3/aud_drv/AudioALSADeviceConfigManager.cpp", 563L
07-20 20:23:31.827   829  3016 E AEE_LIBAEEV: shell: cant create socket with aed: Connection refused
07-20 20:23:31.865   829  3016 E AudioSmartPaController: Error: MUSIC_48000 invalid value, ret = -22
07-20 20:23:32.236   991  1020 E flp     : mnld_screen_monitor_thread: Screen off
07-20 20:23:32.239   819   825 E statsd  : Stats puller failed for tag: 10038 at 210715322858
07-20 20:23:32.262  1046  1233 E [GF_HAL][Device]: module:3, event:2
07-20 20:23:32.262  1046  1233 E [GF_HAL][Device]: start wait event
07-20 20:23:32.266  1986  1986 E PhoneInterfaceManager: queryModemActivityInfo: invalid response
07-20 20:23:32.272   819   825 E statsd  : Stats puller failed for tag: 10011 at 210715322858
07-20 20:23:32.273   819   825 E statsd  : Stats puller failed for tag: 10038 at 210715322858
07-20 20:23:32.676  1046  1319 E [GF_HAL][FingerprintCore]: [authenticate] gid : 0, mAuthType : 1
07-20 20:23:32.676  1046  1319 E [GF_HAL][Device]: [enable_tp] mode = 1.
07-20 20:23:32.679  1046  1319 E android.hardware.biometrics.fingerprint@2.1-service: vendorAcquiredFilter: 1003
07-20 20:23:32.705  2334  2413 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:23:32.767  2334  2412 E perf_hint: createSessionUsingConfig: PerformanceHint cannot create session. PowerHAL is not supported!
07-20 20:23:32.784  1298  1315 E HidlToAidlSessionAdapter: setIgnoreDisplayTouches unsupported in HIDL
07-20 20:23:49.082   819   825 E statsd  : Stats puller failed for tag: 10038 at 227556521243
07-20 20:23:49.084  1298  1348 E SensorManager: sensor or listener is null
07-20 20:23:49.120  1298  1298 E SensorManager: sensor or listener is null
07-20 20:23:49.127  1986  1986 E PhoneInterfaceManager: queryModemActivityInfo: invalid response
07-20 20:23:49.143   819   825 E statsd  : Stats puller failed for tag: 10011 at 227556521243
07-20 20:23:49.143   819   825 E statsd  : Stats puller failed for tag: 10038 at 227556521243
07-20 20:23:49.157   843   843 E sensors-hal-SensorManager: batch failed 27 0 66667000 0 err = -22
07-20 20:23:49.158  1298  1322 E SensorService: sensor batch failed 0x0000001b 66667000 0 err=Invalid argument
07-20 20:23:49.324  1046  1233 E [GF_HAL][Device]: module:3, event:3
07-20 20:23:49.324  1046  1233 E [GF_HAL][Device]: start wait event
07-20 20:23:49.340   991  1020 E flp     : mnld_screen_monitor_thread: Screen on
07-20 20:23:49.340   991  1020 E flp     : mnld_screen_monitor_thread: Wake Monitor Restart
07-20 20:23:59.516   819   825 E statsd  : Stats puller failed for tag: 10038 at 237991972090
07-20 20:23:59.517   991  1020 E flp     : mnld_screen_monitor_thread: Screen off
07-20 20:23:59.528  1046  1233 E [GF_HAL][Device]: module:3, event:2
07-20 20:23:59.528  1046  1233 E [GF_HAL][Device]: start wait event
07-20 20:23:59.540  1986  1986 E PhoneInterfaceManager: queryModemActivityInfo: invalid response
07-20 20:23:59.549   819   825 E statsd  : Stats puller failed for tag: 10011 at 237991972090
07-20 20:23:59.550   819   825 E statsd  : Stats puller failed for tag: 10038 at 237991972090
07-20 20:24:37.271   819   825 E statsd  : Stats puller failed for tag: 10038 at 275746337631
07-20 20:24:37.312  1986  1986 E PhoneInterfaceManager: queryModemActivityInfo: invalid response
07-20 20:24:37.321   819   825 E statsd  : Stats puller failed for tag: 10011 at 275746337631
07-20 20:24:37.321   819   825 E statsd  : Stats puller failed for tag: 10038 at 275746337631
07-20 20:24:37.750   819   825 E statsd  : Stats puller failed for tag: 10038 at 276225661862
07-20 20:24:37.751   819   825 E statsd  : Stats puller failed for tag: 10011 at 276225661862
07-20 20:24:37.751   819   825 E statsd  : Stats puller failed for tag: 10038 at 276225661862
